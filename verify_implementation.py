#!/usr/bin/env python3
"""
Verify the actual implementation of the working hours in the Wiz Aroma system.
"""

import sys
import os
import datetime

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from utils.time_utils import is_open_now


def main():
    print("🔍 Verifying Wiz Aroma Working Hours Implementation")
    print("=" * 60)
    
    # Get current Ethiopian time
    utc_now = datetime.datetime.now(datetime.timezone.utc)
    ethiopia_offset = datetime.timedelta(hours=3)
    ethiopia_now = utc_now + ethiopia_offset
    current_time = ethiopia_now.time()
    
    print(f"Current Ethiopian Time: {ethiopia_now.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Current Time Only: {current_time.strftime('%H:%M:%S')}")
    print()
    
    # Test the actual implementation
    try:
        is_open = is_open_now()
        status = "🟢 OPEN" if is_open else "🔴 CLOSED"
        print(f"Service Status (from is_open_now()): {status}")
        
        if is_open:
            print("✅ The service is currently accepting orders!")
        else:
            print("❌ The service is currently closed.")
        
        print()
        print("📋 Configured Working Hours:")
        print("• Lunch Service: 11:30 AM - 1:30 PM (11:30 - 13:30)")
        print("• Dinner Service: 6:00 PM - 8:30 PM (18:00 - 20:30)")
        print("• Available all days of the week")
        print("• Ethiopian Local Time - Dire Dawa Branch")
        
        # Manual verification
        lunch_start = datetime.time(11, 30)
        lunch_end = datetime.time(13, 30)
        dinner_start = datetime.time(18, 0)
        dinner_end = datetime.time(20, 30)
        
        manual_check = (lunch_start <= current_time <= lunch_end) or \
                      (dinner_start <= current_time <= dinner_end)
        
        print()
        print("🔍 Manual Verification:")
        print(f"Expected Status: {'🟢 OPEN' if manual_check else '🔴 CLOSED'}")
        
        if is_open == manual_check:
            print("✅ Implementation matches expected behavior!")
        else:
            print("❌ Implementation does NOT match expected behavior!")
            print("   This indicates a potential issue in the code.")
        
    except Exception as e:
        print(f"❌ Error testing implementation: {e}")
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

