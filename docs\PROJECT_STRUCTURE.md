# 📁 **Wiz Aroma Food Delivery v2.1 Project Structure**

## **Clean & Professional Codebase Architecture**

---

## 🌟 **System Overview**

The **Wiz Aroma Food Delivery v2.1** system is a production-ready multi-bot Telegram delivery management platform with Firebase-first architecture and automatic data lifecycle management. The system employs a **clean, professional architecture** with specialized bot instances, standardized data patterns, and comprehensive documentation.

### **🤖 Bot Ecosystem v2.1**

## 📂 **Clean Project Structure v2.1**

```text
Wiz-Aroma-V-2.1/
├── 📋 Core Files
│   ├── README.md                    # Main project documentation
│   ├── main.py                     # Main application entry point
│   ├── requirements.txt            # Python dependencies
│   ├── LICENSE                     # Project license
│   └── wiz-aroma-firebase-adminsdk.json # Firebase credentials
│
├── 📚 Documentation (docs/)
│   ├── ACADEMIC_PROPOSAL.md         # Academic project proposal
│   ├── ARCHITECTURE_CLEANUP_SUMMARY.md # v2.1 architectural improvements
│   ├── BOT_STARTUP_GUIDE.md        # System startup guide
│   ├── CHANGELOG.md                # Complete version history
│   ├── DEPLOYMENT.md               # Deployment instructions
│   ├── DOCUMENTATION_UPDATE_SUMMARY.md # Documentation update summary
│   ├── PROJECT_STRUCTURE.md        # This file - project organization
│   ├── RELEASE_NOTES_V2.0.md       # Version 2.0 release notes
│   ├── RELEASE_NOTES_V2.1.md       # Version 2.1 release notes
│   ├── SYSTEM_ARCHITECTURE.md      # Technical architecture documentation
│   └── Wiz Aroma Branch Agreement Format.docx # Business agreement template
│
├── 🎨 Assets (assets/)
│   └── Logo/                       # Project logos and branding
│       ├── Wiz-01.png through Wiz-07.png
│       └── Wiz-10.png
│
├── 🧪 Tests (tests/)
│   └── (Test files will be organized here)
│
├── 📜 Scripts (scripts/)
│   └── verify_clean_architecture.py # Architecture verification script
│
├── 📊 Logs (logs/)
│   ├── bot_*.log                   # Bot operation logs
│   └── errors_*.log                # Error logs
│
└── 📦 Source Code (src/)
    ├── __init__.py                 # Package initialization
    ├── config.py                   # System configuration and environment variables
    ├── bot_instance.py             # Bot instance initialization
    ├── data_models.py              # Data models and structures
    ├── data_storage.py             # Data storage operations
    ├── firebase_db.py              # Firebase database operations
    │
    ├── 🤖 Specialized Bots (bots/)
    │   ├── delivery_bot.py         # Broadcast order assignment bot
    │   ├── management_bot.py       # Personnel & analytics management bot
    │   └── order_track_bot.py      # Internal order tracking bot
    │
    ├── 🎮 Event Handlers (handlers/)
    │   ├── __init__.py
    │   ├── admin_handlers.py       # Administrative functions
    │   ├── delivery_personnel_handlers.py  # Delivery personnel management
    │   ├── favorite_orders_handlers.py     # Favorite orders functionality
    │   ├── location_handlers.py    # Location and delivery management
    │   ├── main_handlers.py        # Main bot interactions
    │   ├── maintenance_handlers.py # System maintenance
    │   ├── order_handlers.py       # Order processing
    │   ├── payment_handlers.py     # Payment processing
    │   └── user_profile_handlers.py # User profile management
    │
    ├── 🛠️ Utilities (utils/)
    │   ├── __init__.py
    │   ├── access_control.py       # Authorization and access control
    │   ├── analytics_counter_system.py  # Analytics and metrics
    │   ├── data_consistency.py     # Data integrity management
    │   ├── data_sync.py           # Data synchronization utilities
    │   ├── delivery_personnel_utils.py  # Delivery personnel utilities
    │   ├── earnings_utils.py       # Earnings calculation utilities
    │   ├── error_handling.py       # Error handling and logging
    │   ├── favorite_orders_sync.py # Favorite orders synchronization
    │   ├── financial_calculations.py   # Financial calculations (50% fee sharing)
    │   ├── handler_registration.py # Handler registration system
    │   ├── helpers.py              # General helper functions
    │   ├── keyboards.py            # Telegram keyboard utilities
    │   ├── logging_utils.py        # Logging configuration
    │   ├── text_utils.py           # Text processing utilities
    │   ├── time_based_reset_utils.py   # Time-based reset functionality
    │   ├── time_utils.py           # Time and date utilities
    │   ├── validation.py           # Input validation utilities
    │   ├── temp_data_manager.py    # Enhanced temporary data management
    │   ├── firebase_business_data.py # Firebase-first business data management
    │   ├── auto_cleanup_service.py # Automatic cleanup service
    │   └── data_management_patterns.py # Standardized data patterns
    │
    └── 📊 Data Layer (data/)
        ├── __init__.py
        └── menus.py                # Menu data structures
```

## 🏗️ **v2.1 Architecture Enhancements**

### **🔥 Firebase-First Data Architecture**

The v2.1 release introduces a clean, professional data architecture with clear separation of concerns:

#### **📊 Data Classification System**

- **Temporary Business Data** (`temp_*`): Automatically cleaned after order completion
- **Persistent User Data**: Stored exclusively in Firebase Firestore
- **Static Configuration** (`config_*`): Locally cached for performance

#### **🧹 Automatic Data Lifecycle Management**

- **Auto Cleanup Service**: Background service monitoring and cleaning temporary data
- **Order Completion Cleanup**: All temporary data removed after order completion
- **Session Timeout Handling**: 30-minute user session cleanup
- **Stale Data Detection**: Orphaned data automatically identified and cleaned
- **Force Cleanup Mechanism**: Hourly comprehensive cleanup to prevent accumulation

#### **⚡ Professional Code Organization**

- **Standardized Naming**: `temp_*`, `config_*`, `firebase_*` conventions
- **Clean Architecture**: Proper separation between business logic and data storage
- **Enterprise Patterns**: Professional data management throughout the codebase
- **Backward Compatibility**: Smooth transition with legacy support maintained

#### **🔧 New Architecture Components**

- `firebase_business_data.py`: Firebase-first business data management
- `auto_cleanup_service.py`: Automatic cleanup service
- `data_management_patterns.py`: Standardized data patterns
- Enhanced `temp_data_manager.py`: Improved temporary data management

---

## 🧹 **Cleanup Summary**

### **✅ Removed Unnecessary Files & Directories**

**Development & Testing Files:**

- `check_firebase_data.py` - Development debugging script
- `clean_firebase_menus.py` - Development utility script
- `debug_favorite_orders_specific.py` - Debug script
- `debug_firebase_favorite_orders.py` - Debug script
- `initialize_firebase_demo.py` - Demo initialization script
- `validate_favorite_orders.py` - Validation script
- `wiz-aroma-testing-firebase-adminsdk.json` - Test credentials

**Redundant Documentation:**

- `BOT_SETUP.md` - Merged into BOT_STARTUP_GUIDE.md
- `FAVORITE_ORDERS_SYNC_DOCUMENTATION.md` - Feature-specific docs
- `FIREBASE_SECURITY_RULES.md` - Merged into main documentation
- `FIREBASE_SETUP.md` - Merged into main documentation
- `SECURITY.md` - Merged into main documentation
- `SECURITY_SETUP.md` - Merged into main documentation

**Unused Directories:**

- `app/` - Alternative app structure (unused)
- `config/` - Alternative configuration (unused)
- `data/` - Local data storage (replaced by Firebase)
- `deployment/` - Deployment scripts (simplified)
- `docs/` - Redundant documentation
- `scripts/` - Development scripts
- `tests/` - Test files (to be reorganized)
- `logs/` - Old log files
- `data_files/` - Local data files (Firebase-exclusive now)
- `assets/` - Redundant assets

**Build & Deployment Files:**

- `Procfile` - Heroku deployment (not needed)
- `firestore.rules` - Firebase rules (managed in console)

### **🎯 Benefits of Cleanup**

- **Reduced Complexity**: Cleaner, more focused codebase
- **Better Organization**: Clear separation of concerns
- **Easier Maintenance**: Less clutter, easier navigation
- **Production Ready**: Only essential files for deployment
- **Documentation Clarity**: Consolidated, up-to-date documentation
- **Firebase-Exclusive**: Removed all local data dependencies

| Bot | Purpose | Status | Access Level |
|-----|---------|--------|--------------|
| 🛍️ **User Bot** | Customer interactions & order placement | ✅ **Fully Operational** | 🌐 Public |
| 👨‍💼 **Admin Bot** | Order management & approval workflow | ✅ **Fully Operational** | 🔒 Restricted |
| 💰 **Finance Bot** | Payment verification & processing | ✅ **Fully Operational** | 🔒 Restricted |
| 🔧 **Maintenance Bot** | System configuration & maintenance | ✅ **Fully Operational** | 🔒 Admin Only |
| 📢 **Notification Bot** | Automated notifications & alerts | ✅ **Fully Operational** | 🤖 Automated |

### **🏗️ Architecture Principles**

- **🔄 Modular Design**: Clear separation of concerns between components
- **📊 Data Consistency**: Dual storage (Firebase + Local JSON backup)
- **⚡ Performance**: Optimized for 50+ concurrent users
- **🛡️ Security**: Role-based access control and secure data handling
- **🔧 Maintainability**: Well-documented, testable codebase

## Core Components

### 1. Bot Instances

The system uses four separate Telegram bot instances, each with its own token and purpose. These instances are initialized in `bot_instance.py`:

```python
# Create bot instances with error handling
try:
    if not TEST_MODE:
        # Normal operation with real tokens
        bot = telebot.TeleBot(BOT_TOKEN)
        admin_bot = telebot.TeleBot(ADMIN_BOT_TOKEN)
        finance_bot = telebot.TeleBot(FINANCE_BOT_TOKEN)
        maintenance_bot = telebot.TeleBot(MAINTENANCE_BOT_TOKEN)
```

Each bot instance is used by its respective handlers to interact with users.

### 2. Data Models

The system uses a combination of in-memory data structures and persistent storage. The in-memory data structures are defined in `data_models.py`:

```python
# Dictionary data storage for in-memory data
user_points = {}  # Stores user points: {user_id: points}
user_order_history = {}  # Stores user order history: {user_id: [order1, order2, ...]}
user_names = {}  # Stores user names: {user_id: name}
user_phone_numbers = {}  # Stores user phone numbers: {user_id: phone_number}
orders = {}  # Active orders being created: {user_id: order_data}
order_status = {}  # Status of orders in progress: {user_id: status}
pending_admin_reviews = {}  # Orders waiting for admin review: {order_number: order_data}
admin_remarks = {}  # Admin remarks on orders: {order_number: remarks}
awaiting_receipt = {}  # Orders waiting for payment receipt: {order_number: order_data}
delivery_locations = {}  # Temporary storage for delivery locations: {user_id: location}
current_order_numbers = {}  # Current order numbers: {user_id: order_number}
user_order_counts = {}  # Count of orders per user: {user_id: count}
favorite_orders = {}  # Favorite orders: {user_id: [order1, order2, ...]}
```

These data structures are loaded from and saved to JSON files for persistence.

### 3. Data Storage

The system uses JSON files for persistent storage. The data storage functions are defined in `data_storage.py`:

```python
def _safe_write_json(filename, data):
    """Safely write data to a JSON file with backup and error handling"""
    try:
        # Ensure directory exists
        directory = os.path.dirname(filename)
        if directory and not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)

        # First create a backup if the file exists
        backup_file(filename)

        # Write the new data
        with open(filename, "w", encoding="utf-8") as f:
            json.dump(data, f, indent=4, ensure_ascii=False)
        return True
    except Exception as e:
        logger.error(f"Error writing to {filename}: {e}")
        return False
```

The system includes functions for loading and saving all data structures, as well as CRUD operations for areas, restaurants, menus, etc.

### 4. Handler Registration

The system uses a centralized handler registration system to avoid circular imports and ensure all handlers are properly registered. The handler registration system is defined in `utils/handler_registration.py`:

```python
class HandlerRegistry:
    """
    Registry for bot handlers to ensure consistent registration.
    """
    
    def __init__(self):
        """Initialize the registry."""
        self.handlers = {
            "user": [],
            "admin": [],
            "finance": [],
            "maintenance": [],
        }
        self.registered = False
```

Handlers are registered using the `@register_handler` decorator:

```python
@register_handler("user", func=lambda message: message.text == "🍽️ Order Food")
def handle_order_food(message):
    # Handler implementation
    pass
```

### 5. Error Handling

The system includes comprehensive error handling to ensure robustness. Error handling utilities are defined in `utils/error_handling.py`:

```python
def handle_exceptions(func):
    """Decorator to handle exceptions in bot handlers"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.error(f"Error in {func.__name__}: {str(e)}", exc_info=True)
            # Try to get the message object from args
            message = None
            for arg in args:
                if hasattr(arg, 'chat') and hasattr(arg.chat, 'id'):
                    message = arg
                    break
            
            if message:
                try:
                    bot.send_message(
                        message.chat.id,
                        "❌ An error occurred. Please try again or contact support."
                    )
                except Exception as send_error:
                    logger.error(f"Error sending error message: {str(send_error)}")
    
    return wrapper
```

### 6. Logging

The system uses Python's built-in logging module for logging. Logging utilities are defined in `utils/logging_utils.py`:

```python
def get_logger(name=None):
    """Get a logger with the specified name"""
    if name is None:
        name = __name__
    
    logger = logging.getLogger(name)
    
    # Only configure the logger if it hasn't been configured yet
    if not logger.handlers:
        logger.setLevel(logging.INFO)
        
        # Create a file handler
        file_handler = logging.FileHandler('bot.log')
        file_handler.setLevel(logging.INFO)
        
        # Create a console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # Create a formatter
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # Add the handlers to the logger
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
    
    return logger
```

## Bot Handlers

The system is organized around a set of handlers for each bot, each responsible for a specific part of the functionality.

### 1. User Bot Handlers

#### Main Handlers (`main_handlers.py`)

The main handlers handle the main menu and general commands like `/start` and `/help`:

```python
@bot.message_handler(commands=["start"])
def start(message):
    """Handle the /start command"""
    try:
        user_id = message.from_user.id
        
        # Clean up any existing order data for this user
        clean_up_order_data(user_id)
        
        # Get user's first name
        first_name = message.from_user.first_name
        
        # Store user's name if not already stored
        if user_id not in user_names:
            user_names[user_id] = first_name
            save_user_data()
        
        # Create welcome message
        welcome_text = (
            f"👋 Welcome to Wiz Aroma Food Delivery, {first_name}!\n\n"
            "I'm your personal food delivery assistant. "
            "You can order food from various restaurants around campus.\n\n"
            "🕒 *Operating Hours*:\n"
            "Weekdays: 5:30-7:30 and 11:30-14:30\n"
            "Weekends: 5:30-14:30\n\n"
            "What would you like to do today?"
        )
        
        # Create main menu markup
        markup = get_main_menu_markup()
        
        # Send welcome message with main menu
        bot.send_message(
            message.chat.id, welcome_text, reply_markup=markup, parse_mode="Markdown"
        )
        
    except Exception as e:
        logger.error(f"Error in start: {str(e)}")
        bot.send_message(
            message.chat.id, "❌ An error occurred. Please try again or contact support."
        )
```

#### Order Handlers (`order_handlers.py`)

The order handlers handle the order flow, from selecting an area to confirming an order:

```python
@bot.message_handler(
    func=lambda message: message.text and message.text == "🍽️ Order Food"
)
def handle_order_food(message):
    """Handle the order food button"""
    try:
        user_id = message.from_user.id
        
        # Check if the service is open
        if not is_open_now():
            closed_message = (
                "⏰ Sorry, we're currently closed.\n\n"
                "🕒 *Operating Hours*:\n"
                "Weekdays: 5:30-7:30 and 11:30-14:30\n"
                "Weekends: 5:30-14:30\n\n"
                "Please come back during our operating hours!"
            )
            bot.send_message(
                message.chat.id, closed_message, parse_mode="Markdown"
            )
            return
        
        # Clean up any existing order data for this user
        clean_up_order_data(user_id)
        
        # Get areas markup
        markup = get_areas_markup()
        
        # Send areas message
        bot.send_message(
            message.chat.id,
            "Please select an area:",
            reply_markup=markup,
        )
        
        # Update order status
        order_status[user_id] = "SELECTING_AREA"
        
    except Exception as e:
        logger.error(f"Error in handle_order_food: {str(e)}")
        bot.send_message(
            message.chat.id, "❌ An error occurred. Please try again or contact support."
        )
```

#### Payment Handlers (`payment_handlers.py`)

The payment handlers handle payment processing, including selecting payment methods and verifying payments:

```python
@bot.message_handler(
    func=lambda message: order_status.get(message.from_user.id) == "AWAITING_PAYMENT_METHOD"
    and message.text
    and message.text in ["📱 Telebirr", "🏦 CBE", "🏦 BOA", "💫 Use Points"]
)
def handle_payment_method(message):
    """Handle payment method selection"""
    try:
        user_id = message.from_user.id
        
        # Check if user has an active order
        if user_id not in orders:
            bot.send_message(message.chat.id, "❌ No active order found.")
            return
        
        # Get the payment method
        payment_method = message.text
        
        # Store the payment method in the order
        orders[user_id]["payment_method"] = payment_method
        
        # Handle different payment methods
        if payment_method == "💫 Use Points":
            # Check if user has enough points
            points_balance = get_points_balance(user_id)
            delivery_fee = orders[user_id].get("delivery_fee", 0)
            
            if points_balance >= delivery_fee:
                # User has enough points
                markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
                markup.add(
                    types.KeyboardButton("✅ Confirm Points Use"),
                    types.KeyboardButton("❌ Cancel Points Use"),
                )
                
                bot.send_message(
                    message.chat.id,
                    f"You have {points_balance} points. "
                    f"This order will use {delivery_fee} points for the delivery fee.\n\n"
                    f"Do you want to proceed?",
                    reply_markup=markup,
                )
                
                # Update order status
                order_status[user_id] = "AWAITING_POINTS_CONFIRMATION"
            else:
                # User doesn't have enough points
                markup = get_payment_method_markup()
                
                bot.send_message(
                    message.chat.id,
                    f"❌ You don't have enough points. You have {points_balance} points, "
                    f"but this order requires {delivery_fee} points.\n\n"
                    f"Please select another payment method:",
                    reply_markup=markup,
                )
        else:
            # Handle other payment methods
            if payment_method == "📱 Telebirr":
                payment_info = (
                    f"Please send {orders[user_id].get('total_price', 0)} birr to:\n\n"
                    f"📱 *Telebirr*: {TELEBIRR_PHONE} ({TELEBIRR_NAME})\n\n"
                    f"After sending the payment, please send a screenshot of the payment confirmation."
                )
            elif payment_method == "🏦 CBE":
                payment_info = (
                    f"Please transfer {orders[user_id].get('total_price', 0)} birr to:\n\n"
                    f"🏦 *CBE*: {CBE_ACCOUNT_NUMBER} ({CBE_ACCOUNT_NAME})\n\n"
                    f"After sending the payment, please send a screenshot of the payment confirmation."
                )
            elif payment_method == "🏦 BOA":
                payment_info = (
                    f"Please transfer {orders[user_id].get('total_price', 0)} birr to:\n\n"
                    f"🏦 *BOA*: {BOA_ACCOUNT_NUMBER} ({BOA_ACCOUNT_NAME})\n\n"
                    f"After sending the payment, please send a screenshot of the payment confirmation."
                )
            
            # Create markup with cancel button
            markup = types.ReplyKeyboardMarkup(row_width=1, resize_keyboard=True)
            markup.add(types.KeyboardButton("❌ Cancel Payment"))
            
            bot.send_message(
                message.chat.id,
                payment_info,
                reply_markup=markup,
                parse_mode="Markdown",
            )
            
            # Update order status
            order_status[user_id] = "AWAITING_RECEIPT"
            
            # Add order to awaiting receipt
            order_number = orders[user_id].get("order_number", "")
            awaiting_receipt[order_number] = {
                "user_id": user_id,
                "order": orders[user_id],
                "payment_method": payment_method,
            }
            
            # Save awaiting receipt data
            from src.data_storage import save_awaiting_receipt
            save_awaiting_receipt(awaiting_receipt)
        
    except Exception as e:
        logger.error(f"Error in handle_payment_method: {str(e)}")
        bot.send_message(
            message.chat.id, "❌ An error occurred. Please try again or contact support."
        )
```

### 2. Admin Bot Handlers (`admin_handlers.py`)

The admin handlers handle admin actions like reviewing orders, adding remarks, and approving/rejecting orders:

```python
@admin_bot.callback_query_handler(
    func=lambda call: call.data.startswith(
        ("new_confirm_", "new_decline_", "new_note_")
    )
)
def handle_new_admin_action(call):
    """Handle new admin actions on orders"""
    try:
        # Answer callback immediately to clear loading state
        admin_bot.answer_callback_query(
            callback_query_id=call.id,
            text="Processing your request...",
            show_alert=False,
        )
        
        # Parse callback data
        action_type, order_number = call.data.split("_", 2)[1:]
        logger.info(
            f"Admin {call.from_user.id} processing {action_type} for order {order_number}"
        )
        
        # Reload pending reviews to ensure we have the latest data
        reload_pending_admin_reviews()
        
        # Check if order exists
        if order_number not in pending_admin_reviews:
            admin_bot.send_message(
                call.message.chat.id,
                "⚠️ This order has already been processed or doesn't exist.",
            )
            return
        
        # Get order details
        order_details = pending_admin_reviews[order_number]
        user_id = order_details["user_id"]
        order = order_details["order"]
        
        # Handle different actions
        if action_type == "confirm":  # Approve
            # Update order status
            if user_id in order_status:
                order_status[user_id] = "AWAITING_PAYMENT_METHOD"
            
            # Remove from pending reviews
            del pending_admin_reviews[order_number]
            save_pending_admin_reviews(pending_admin_reviews)
            
            # Update message to show approved
            admin_bot.edit_message_text(
                f"✅ *ORDER #{order_number} APPROVED*\n\n{call.message.text.split('DETAILS')[1]}",
                call.message.chat.id,
                call.message.message_id,
                parse_mode="Markdown",
            )
            
            # Notify user
            try:
                # Create payment method markup
                markup = get_payment_method_markup()
                
                # Send approval message to user
                bot.send_message(
                    user_id,
                    f"✅ Your order #{order_number} has been approved!\n\n"
                    f"Please select a payment method:",
                    reply_markup=markup,
                )
                
                logger.info(f"User {user_id} notified of order approval")
            except Exception as notify_error:
                logger.error(f"Error notifying user of approval: {notify_error}")
                admin_bot.send_message(
                    call.message.chat.id,
                    f"⚠️ Error notifying customer: {notify_error}",
                )
        
        elif action_type == "decline":  # Reject
            # Update order status
            if user_id in order_status:
                order_status[user_id] = "REJECTED"
            
            # Remove from pending reviews
            del pending_admin_reviews[order_number]
            save_pending_admin_reviews(pending_admin_reviews)
            
            # Update message to show rejected
            admin_bot.edit_message_text(
                f"❌ *ORDER #{order_number} REJECTED*\n\n{call.message.text.split('DETAILS')[1]}",
                call.message.chat.id,
                call.message.message_id,
                parse_mode="Markdown",
            )
            
            # Notify user
            try:
                # Create main menu markup
                markup = get_main_menu_markup()
                
                # Get rejection reason if available
                rejection_reason = ""
                if order_number in admin_remarks:
                    rejection_reason = f"\n\nReason: {admin_remarks[order_number]}"
                
                # Send rejection message to user
                bot.send_message(
                    user_id,
                    f"❌ Your order #{order_number} has been rejected.{rejection_reason}\n\n"
                    f"Please contact support if you have any questions.",
                    reply_markup=markup,
                )
                
                logger.info(f"User {user_id} notified of order rejection")
            except Exception as notify_error:
                logger.error(f"Error notifying user of rejection: {notify_error}")
                admin_bot.send_message(
                    call.message.chat.id,
                    f"⚠️ Error notifying customer: {notify_error}",
                )
        
        # Handle note (remarks)
        elif action_type == "note":
            # Ask admin for notes
            admin_bot.send_message(
                call.message.chat.id,
                f"📝 Please enter a note for order #{order_number}:",
                reply_markup=types.ForceReply(selective=True),
            )
            logger.info(
                f"Admin {call.from_user.id} adding note for order {order_number}"
            )
    
    except Exception as e:
        logger.error(f"Error in handle_new_admin_action: {e}", exc_info=True)
        admin_bot.send_message(
            call.message.chat.id,
            f"⚠️ An error occurred: {str(e)}",
        )
```

### 3. Finance Bot Handlers (`payment_handlers.py`)

The finance handlers handle payment verification:

```python
@finance_bot.message_handler(content_types=["photo"])
def handle_finance_receipt(message):
    """Handle receipt photos sent to finance bot"""
    try:
        # Check if the user is authorized
        if str(message.from_user.id) != FINANCE_CHAT_ID:
            finance_bot.reply_to(message, "⚠️ You are not authorized to use this bot.")
            return
        
        # Check if there's a caption with order number
        if not message.caption or "#" not in message.caption:
            finance_bot.reply_to(
                message,
                "⚠️ Please include the order number in the caption (e.g., #12345).",
            )
            return
        
        # Extract order number from caption
        order_number = message.caption.split("#")[1].split()[0].strip()
        
        # Create verification buttons
        markup = types.InlineKeyboardMarkup()
        markup.row(
            types.InlineKeyboardButton(
                "✅ Verify Payment", callback_data=f"verify_{order_number}"
            ),
            types.InlineKeyboardButton(
                "❌ Reject Payment", callback_data=f"reject_{order_number}"
            ),
        )
        
        # Forward the message to finance with verification buttons
        finance_bot.reply_to(
            message,
            f"Payment receipt for order #{order_number}. Please verify or reject:",
            reply_markup=markup,
        )
        
        logger.info(f"Finance received payment receipt for order #{order_number}")
    
    except Exception as e:
        logger.error(f"Error in handle_finance_receipt: {str(e)}")
        finance_bot.reply_to(
            message, "❌ An error occurred. Please try again or contact support."
        )
```

### 4. Maintenance Bot Handlers (`maintenance_handlers.py`)

The maintenance handlers handle system configuration and maintenance:

```python
@maintenance_bot.message_handler(func=lambda message: message.text == "Areas")
def show_areas_menu(message):
    """Show the areas management menu"""
    user_id = message.from_user.id
    
    if not is_authorized(user_id):
        return
    
    markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
    markup.add(
        types.KeyboardButton("List Areas"),
        types.KeyboardButton("Add Area"),
        types.KeyboardButton("Update Area"),
        types.KeyboardButton("Delete Area"),
        types.KeyboardButton("Back to Main Menu"),
    )
    
    maintenance_bot.send_message(
        user_id,
        "Areas Management. What would you like to do?",
        reply_markup=markup,
    )
```

## Order Flow

The order flow is the main process in the system, involving all four bots and multiple data structures.

### 1. Order Placement

1. User selects "🍽️ Order Food" in the User Bot
2. User selects an area
3. User selects a restaurant
4. User adds menu items
5. User adds order description (optional)
6. User selects delivery location
7. User provides delivery name
8. User provides phone number
9. User reviews order
10. User confirms order
11. Order is submitted for admin review

### 2. Admin Review

1. Admin receives order notification in the Admin Bot
2. Admin reviews order details
3. Admin adds remarks (optional)
4. Admin approves or rejects order
5. User is notified of admin decision

### 3. Payment Processing

1. User selects payment method in the User Bot
2. If using points:
   - Points balance is checked
   - Points are deducted
   - Order is marked as paid
3. If using other payment methods:
   - User sends payment receipt
   - Receipt is forwarded to Finance Bot
   - Finance verifies payment
   - User is notified of payment verification

### 4. Order Fulfillment

1. Order is confirmed
2. Order is processed for delivery
3. Order is delivered
4. Order is saved to history
5. Points are awarded
6. Order data is cleaned up

## Data Flow

The system uses a combination of in-memory data structures and persistent storage:

1. In-memory data structures are defined in `data_models.py`
2. Data is loaded from JSON files on startup
3. Data is modified during operation
4. Data is periodically saved to JSON files
5. Data is backed up before writing

## Deployment

The system is designed to be deployed on Railway or similar platforms:

1. Environment variables are used for configuration
2. A Procfile is included for Railway deployment
3. The system can be run locally or in a container

## Conclusion

The Wiz Aroma Food Delivery system is a comprehensive solution for food delivery services, with a modular architecture and clear separation of concerns. It includes four separate bots, each with a specific role, and a robust data storage system for persistence.

The system is designed to be scalable, maintainable, and easy to deploy, making it suitable for campus food delivery services and similar applications.
