"""
Data storage functions for the Wiz Aroma Food Delivery system.
Contains functions for loading and saving persistent data.
"""

import json
import datetime
import os
from typing import Dict, List, Any, Optional
import logging
import copy

# Import Firebase functions
from src.firebase_db import (
    get_user_points,
    update_user_points,
    update_user_points_batch,
    get_user_names,
    update_user_name,
    update_user_names_batch,
    get_user_phone_numbers,
    update_user_phone_number,
    update_user_phone_numbers_batch,
    get_user_emails,
    update_user_email,
    update_user_emails_batch,
    get_user_order_history,
    add_order_to_history,
    update_user_order_history_batch,

    update_favorite_orders,
    add_favorite_order,
    delete_favorite_order,
    update_favorite_orders_batch,
    get_current_orders,
    update_current_order,
    delete_current_order,
    update_current_orders_batch,
    get_order_status,
    update_order_status,
    update_order_status_batch,
    get_pending_admin_reviews,
    add_pending_admin_review,
    delete_pending_admin_review,
    update_pending_admin_reviews_batch,
    get_admin_remarks,
    update_admin_remark,
    update_admin_remarks_batch,
    get_awaiting_receipt,
    add_awaiting_receipt,
    delete_awaiting_receipt,
    update_awaiting_receipt_batch,
    get_data,
    set_data,
    update_data,
)

from src.config import (
    POINTS_FILE,
    ORDER_HISTORY_FILE,
    USER_NAMES_FILE,
    USER_PHONE_NUMBERS_FILE,
    USER_EMAILS_FILE,
    AREAS_FILE,
    RESTAURANTS_FILE,
    MENUS_FILE,
    DELIVERY_LOCATIONS_FILE,
    DELIVERY_FEES_FILE,
    FAVORITE_ORDERS_FILE,
    CURRENT_ORDERS_FILE,
    ORDER_STATUS_FILE,
    PENDING_ADMIN_REVIEWS_FILE,
    ADMIN_REMARKS_FILE,
    AWAITING_RECEIPT_FILE,
    DELIVERY_LOCATIONS_TEMP_FILE,
    USER_ORDER_COUNTS_FILE,
    CURRENT_ORDER_NUMBERS_FILE,
    logger,
)

from src.data_models import (
    user_points,
    user_order_history,
    user_names,
    user_phone_numbers,
    user_emails,
    orders,
    order_status,
    pending_admin_reviews,
    admin_remarks,
    awaiting_receipt,
    delivery_locations,
    current_order_numbers,
    favorite_orders,
    areas_data,
    restaurants_data,
    menus_data,
    delivery_locations_data,
    delivery_fees_data,
    delivery_personnel,
    delivery_personnel_assignments,
    delivery_personnel_availability,
    delivery_personnel_capacity,
    delivery_personnel_zones,
    delivery_personnel_performance,
    delivery_personnel_earnings,
)

# Set this to always use Firebase
USE_FIREBASE = True

# Initialize empty dictionaries to temporarily store loaded data
areas_data = {"areas": []}
restaurants_data = {"restaurants": []}
menus_data = {"default_menu_items": [], "restaurant_menus": {}}
delivery_locations_data = {"delivery_locations": []}
delivery_fees_data = {"delivery_fees": []}


def clean_order_history_data():
    """Remove 'is_creating_favorite' field from all order history data"""
    try:
        # Load order history data
        order_history = load_order_history()

        # Clean the data
        for user_id, orders in order_history.items():
            for order in orders:
                if "is_creating_favorite" in order:
                    del order["is_creating_favorite"]

        # Save the cleaned data
        update_user_order_history_batch(order_history)
    except Exception as e:
        logger.error(f"Error cleaning order history data: {e}")


def initialize_areas_from_config():
    """Initialize areas_data from the static config if no areas exist"""
    global areas_data

    # Only initialize if no areas exist
    if not areas_data.get("areas", []):
        areas = []
        area_id = 1

        # Get the area names from the static config
        from src.config import restaurants as config_restaurants

        for area_name in config_restaurants:
            # Add the area
            areas.append({"id": area_id, "name": area_name})
            area_id += 1

        # Update areas_data
        areas_data["areas"] = areas

        # Save to Firebase
        save_areas_data()

        logger.info(f"Initialized {len(areas)} areas from static config")
    return areas_data


def initialize_restaurants_from_config():
    """Initialize restaurants_data from the static config if no restaurants exist"""
    global restaurants_data, areas_data

    # Only initialize if no restaurants exist
    if not restaurants_data.get("restaurants", []):
        restaurants = []

        # Get the restaurants from the static config
        from src.config import restaurants as config_restaurants

        # First, ensure areas are initialized
        areas = areas_data.get("areas", [])
        if not areas:
            initialize_areas_from_config()
            areas = areas_data.get("areas", [])

        # Create a mapping of area names to IDs
        area_name_to_id = {area["name"]: area["id"] for area in areas}

        # Now add all restaurants from the config
        for area_name, area_restaurants in config_restaurants.items():
            area_id = area_name_to_id.get(area_name)
            if area_id:
                for restaurant_id, restaurant_info in area_restaurants.items():
                    # Add the restaurant
                    restaurants.append(
                        {
                            "id": int(restaurant_id),
                            "name": restaurant_info["name"],
                            "area_id": area_id,
                        }
                    )

        # Update restaurants_data
        restaurants_data["restaurants"] = restaurants

        # Save to Firebase
        save_restaurants_data()

        logger.info(f"Initialized {len(restaurants)} restaurants from static config")
    return restaurants_data


def load_user_data():
    """Load all user data from Firebase - only loads the necessary configuration data"""
    global areas_data, restaurants_data, menus_data, delivery_locations_data, delivery_fees_data, favorite_orders, user_points, user_order_history, user_names, user_phone_numbers, user_emails
    global delivery_personnel, delivery_personnel_assignments, delivery_personnel_availability, delivery_personnel_capacity, delivery_personnel_zones, delivery_personnel_performance

    # Load from Firebase
    # Load user points
    points_data = get_user_points()
    user_points.update(points_data)
    logger.info(f"Loaded points for {len(points_data)} users from Firebase")

    # Load order history
    order_history_data = get_user_order_history()
    user_order_history.update(order_history_data)
    logger.info(
        f"Loaded order history for {len(order_history_data)} users from Firebase"
    )

    # Load user names
    names_data = get_user_names()
    user_names.update(names_data)
    logger.info(f"Loaded names for {len(names_data)} users from Firebase")

    # Load user phone numbers
    phone_numbers_data = get_user_phone_numbers()
    user_phone_numbers.update(phone_numbers_data)
    logger.info(
        f"Loaded phone numbers for {len(phone_numbers_data)} users from Firebase"
    )

    # Load user emails
    emails_data = get_user_emails()
    user_emails.update(emails_data)
    logger.info(f"Loaded emails for {len(emails_data)} users from Firebase")

    # Load favorite orders
    try:
        # Get from Firebase using the Firebase function (import it specifically)
        from src.firebase_db import get_favorite_orders as get_firebase_favorite_orders
        favorites_data = get_firebase_favorite_orders()

        # Clear existing data and update with Firebase data
        favorite_orders.clear()
        favorite_orders.update(favorites_data)

        logger.info(
            f"Loaded favorite orders for {len(favorites_data)} users from Firebase"
        )

        # Validate persistence after loading
        try:
            persistence_valid = ensure_favorite_orders_persistence()
            if not persistence_valid:
                logger.warning("⚠️ Favorite orders persistence validation failed during startup")
        except Exception as persist_e:
            logger.warning(f"Error validating favorite orders persistence during startup: {persist_e}")

        # Sync favorite orders with current database values
        try:
            from src.utils.favorite_orders_sync import sync_all_favorite_orders
            sync_results = sync_all_favorite_orders()
            if sync_results["orders_updated"] > 0:
                logger.info(f"Synchronized {sync_results['orders_updated']} favorite orders with current data")
        except ImportError:
            logger.debug("Favorite orders sync module not available during startup")
        except Exception as sync_e:
            logger.warning(f"Error syncing favorite orders during startup: {sync_e}")

    except Exception as e:
        logger.error(f"Error loading favorite orders: {e}")
        # Initialize empty dictionary to avoid errors
        favorite_orders.clear()
        favorite_orders.update({})
        logger.info("Initialized empty favorite orders dictionary")

    # Load configuration data EXCLUSIVELY from Firebase
    areas_data = get_data("areas") or {"areas": []}
    restaurants_data = get_data("restaurants") or {"restaurants": []}

    # Load menus data with additional validation
    try:
        menus_data_from_firebase = get_data("menus")
        if menus_data_from_firebase and isinstance(menus_data_from_firebase, dict):
            # Ensure restaurant_menus exists and is a dictionary
            if "restaurant_menus" not in menus_data_from_firebase or not isinstance(
                menus_data_from_firebase["restaurant_menus"], dict
            ):
                logger.warning(
                    "restaurant_menus missing or not a dictionary in Firebase data. Initializing..."
                )
                menus_data_from_firebase["restaurant_menus"] = {}

            # Ensure default_menu_items exists
            if "default_menu_items" not in menus_data_from_firebase:
                logger.warning(
                    "default_menu_items missing in Firebase data. Initializing..."
                )
                menus_data_from_firebase["default_menu_items"] = []

            menus_data = menus_data_from_firebase
        else:
            logger.warning(
                "Invalid or missing menus_data from Firebase. Initializing with defaults..."
            )
            menus_data = {
                "default_menu_items": [],
                "restaurant_menus": {},
            }
    except Exception as e:
        logger.error(f"Error loading menus data: {e}")
        menus_data = {
            "default_menu_items": [],
            "restaurant_menus": {},
        }

    logger.info(
        f"Loaded menus data with {len(menus_data.get('restaurant_menus', {}))} restaurant menus"
    )

    delivery_locations_data = get_data("delivery_locations") or {
        "delivery_locations": []
    }
    delivery_fees_data = get_data("delivery_fees") or {"delivery_fees": []}

    # Log what data was loaded from Firebase
    logger.info(f"Loaded {len(areas_data.get('areas', []))} areas from Firebase")
    logger.info(f"Loaded {len(restaurants_data.get('restaurants', []))} restaurants from Firebase")
    logger.info(f"Loaded {len(delivery_locations_data.get('delivery_locations', []))} delivery locations from Firebase")
    logger.info(f"Loaded {len(delivery_fees_data.get('delivery_fees', []))} delivery fees from Firebase")

    # REMOVED: Static config initialization - ALL data must come from Firebase
    # This ensures data consistency and prevents ID mismatches
    if not areas_data.get("areas", []):
        logger.warning("No areas found in Firebase. Areas must be added via maintenance bot.")

    if not restaurants_data.get("restaurants", []):
        logger.warning("No restaurants found in Firebase. Restaurants must be added via maintenance bot.")

    if not delivery_locations_data.get("delivery_locations", []):
        logger.warning("No delivery locations found in Firebase. Delivery locations must be added via maintenance bot.")

    if not delivery_fees_data.get("delivery_fees", []):
        logger.warning("No delivery fees found in Firebase. Delivery fees must be added via maintenance bot.")

    # Load delivery personnel data
    try:
        delivery_personnel.clear()
        delivery_personnel.update(load_delivery_personnel_data())
        logger.info(f"Loaded {len(delivery_personnel)} delivery personnel records")

        delivery_personnel_assignments.clear()
        delivery_personnel_assignments.update(load_delivery_personnel_assignments_data())
        logger.info(f"Loaded {len(delivery_personnel_assignments)} delivery assignments")

        delivery_personnel_availability.clear()
        delivery_personnel_availability.update(load_delivery_personnel_availability_data())
        logger.info(f"Loaded availability for {len(delivery_personnel_availability)} personnel")

        delivery_personnel_capacity.clear()
        delivery_personnel_capacity.update(load_delivery_personnel_capacity_data())
        logger.info(f"Loaded capacity for {len(delivery_personnel_capacity)} personnel")

        delivery_personnel_zones.clear()
        delivery_personnel_zones.update(load_delivery_personnel_zones_data())
        logger.info(f"Loaded zones for {len(delivery_personnel_zones)} personnel")

        delivery_personnel_performance.clear()
        delivery_personnel_performance.update(load_delivery_personnel_performance_data())
        logger.info(f"Loaded performance data for {len(delivery_personnel_performance)} personnel")

    except Exception as e:
        logger.error(f"Error loading delivery personnel data: {e}")
        # Initialize empty data structures if loading fails
        delivery_personnel.clear()
        delivery_personnel_assignments.clear()
        delivery_personnel_availability.clear()
        delivery_personnel_capacity.clear()
        delivery_personnel_zones.clear()
        delivery_personnel_performance.clear()

    logger.info("Finished loading all user data from Firebase")


def initialize_data_files():
    """
    DEPRECATED: Initialize all data files with empty data if they don't exist
    This function is deprecated as we now use Firebase for all persistent data.
    Kept for backward compatibility only.
    """
    logger.warning("initialize_data_files() is deprecated - using Firebase for data storage")
    pass


def backup_file(filename):
    """
    DEPRECATED: Create a backup of a file if it exists
    This function is deprecated as we now use Firebase for all persistent data.
    Kept for backward compatibility only.
    """
    logger.warning("backup_file() is deprecated - using Firebase for data storage")
    pass


# New load functions for previously in-memory data
def load_points() -> Dict[str, int]:
    """Load points data from Firebase"""
    # Get from Firebase
    loaded_points = get_user_points()
    # Update in-memory points
    user_points.update(loaded_points)
    return loaded_points


def load_order_history() -> Dict[str, List[Dict[str, Any]]]:
    """Load order history data from Firebase"""
    # Get from Firebase
    order_history_data = get_user_order_history()
    return order_history_data


def load_user_names() -> Dict[str, str]:
    """Load user names data from Firebase"""
    # Get from Firebase
    names_data = get_user_names()
    return names_data


def load_user_phone_numbers() -> Dict[str, str]:
    """Load user phone numbers data from Firebase"""
    # Get from Firebase
    phone_numbers_data = get_user_phone_numbers()
    return phone_numbers_data


def load_user_emails() -> Dict[str, str]:
    """Load user email addresses data from Firebase"""
    # Get from Firebase
    emails_data = get_user_emails()
    return emails_data


def load_favorite_orders() -> Dict[str, List[Dict[str, Any]]]:
    """Load favorite orders data from Firebase"""
    # Get from Firebase using the Firebase function
    from src.firebase_db import get_favorite_orders as get_firebase_favorite_orders
    favorites_data = get_firebase_favorite_orders()
    return favorites_data


def load_current_orders() -> Dict[str, Dict[str, Any]]:
    """Load current orders data from Firebase"""
    # Get from Firebase
    current_orders_data = get_current_orders()
    return current_orders_data


def load_order_status() -> Dict[str, str]:
    """Load order status data from Firebase"""
    # Get from Firebase
    order_status_data = get_order_status()
    return order_status_data


def load_pending_admin_reviews() -> Dict[str, Dict[str, Any]]:
    """Load pending admin reviews data from Firebase"""
    # Get from Firebase
    pending_admin_reviews_data = get_pending_admin_reviews()
    return pending_admin_reviews_data


def load_admin_remarks() -> Dict[str, str]:
    """Load admin remarks data from Firebase"""
    # Get from Firebase
    admin_remarks_data = get_admin_remarks()
    return admin_remarks_data


def load_awaiting_receipt() -> Dict[str, Any]:
    """Load awaiting receipt data from Firebase"""
    # Get from Firebase
    awaiting_receipt_data = get_awaiting_receipt()
    return awaiting_receipt_data


def load_delivery_locations_temp() -> Dict[str, str]:
    """Load temporary delivery locations data from Firebase"""
    # Get from Firebase
    locations_data = get_data("delivery_locations_temp") or {}
    return locations_data


def load_user_order_counts() -> Dict[str, int]:
    """Load user order counts data from Firebase"""
    # Get from Firebase
    order_counts_data = get_data("user_order_counts") or {}
    return order_counts_data


def load_current_order_numbers() -> Dict[str, str]:
    """Load current order numbers data from Firebase"""
    # Get from Firebase
    current_order_numbers_data = get_data("current_order_numbers") or {}
    return current_order_numbers_data


def load_delivery_personnel_data() -> Dict[str, Dict[str, Any]]:
    """Load delivery personnel data from Firebase"""
    # Get from Firebase
    personnel_data = get_data("delivery_personnel")
    return personnel_data if personnel_data else {}


def load_delivery_personnel_assignments_data() -> Dict[str, Dict[str, Any]]:
    """Load delivery personnel assignments from Firebase"""
    # Get from Firebase
    assignments_data = get_data("delivery_personnel_assignments")
    return assignments_data if assignments_data else {}


def load_delivery_personnel_assignments() -> Dict[str, Dict[str, Any]]:
    """Alias for load_delivery_personnel_assignments_data for consistency"""
    return load_delivery_personnel_assignments_data()


def load_delivery_personnel_availability_data() -> Dict[str, str]:
    """Load delivery personnel availability from Firebase"""
    # Get from Firebase
    availability_data = get_data("delivery_personnel_availability")
    return availability_data if availability_data else {}


def load_delivery_personnel_capacity_data() -> Dict[str, int]:
    """Load delivery personnel capacity from Firebase"""
    # Get from Firebase
    capacity_data = get_data("delivery_personnel_capacity")
    return capacity_data if capacity_data else {}


def load_delivery_personnel_zones_data() -> Dict[str, List[str]]:
    """Load delivery personnel zones from Firebase"""
    # Get from Firebase
    zones_data = get_data("delivery_personnel_zones")
    return zones_data if zones_data else {}


def load_delivery_personnel_performance_data() -> Dict[str, Dict[str, Any]]:
    """Load delivery personnel performance from Firebase"""
    # Get from Firebase
    performance_data = get_data("delivery_personnel_performance")
    return performance_data if performance_data else {}


def load_delivery_personnel_earnings_data() -> Dict[str, Dict[str, Any]]:
    """Load delivery personnel earnings from Firebase"""
    # Get from Firebase
    earnings_data = get_data("delivery_personnel_earnings")
    return earnings_data if earnings_data else {}


def _load_json_file(filename: str, default_value: Any) -> Any:
    """
    DEPRECATED: Legacy function kept for compatibility
    This function is deprecated as we now use Firebase for all data loading.
    """
    logger.warning("_load_json_file() is deprecated - using Firebase for data loading")
    return default_value


# Save functions for previously in-memory data
def save_points(data: Dict[str, int]) -> bool:
    """Save points data to Firebase"""
    # Save to Firebase
    return update_user_points_batch(data)


def save_order_history(data: Dict[str, List[Dict[str, Any]]]) -> bool:
    """Save order history data to Firebase"""
    # Save to Firebase
    return update_user_order_history_batch(data)


def save_user_names(data: Dict[str, str]) -> bool:
    """Save user names data to Firebase"""
    # Save to Firebase
    return update_user_names_batch(data)


def save_user_phone_numbers(data: Dict[str, str]) -> bool:
    """Save user phone numbers data to Firebase"""
    # Save to Firebase
    return update_user_phone_numbers_batch(data)


def save_user_emails(data: Dict[str, str]) -> bool:
    """Save user email addresses data to Firebase"""
    # Save to Firebase
    return update_user_emails_batch(data)


def save_favorite_orders(data: Dict[str, List[Dict[str, Any]]]) -> bool:
    """Save favorite orders data to Firebase"""
    # Save to Firebase
    return update_favorite_orders_batch(data)


def save_current_orders(data: Dict[str, Dict[str, Any]]) -> bool:
    """Save current orders data to Firebase"""
    # Save to Firebase
    return update_current_orders_batch(data)


def save_order_status(data: Dict[str, str]) -> bool:
    """Save order status data to Firebase"""
    # Save to Firebase
    return update_order_status_batch(data)


def save_pending_admin_reviews(data: Dict[str, Dict[str, Any]]) -> bool:
    """Save pending admin reviews data to Firebase"""
    # Save to Firebase
    return update_pending_admin_reviews_batch(data)


def save_admin_remarks(data: Dict[str, str]) -> bool:
    """Save admin remarks data to Firebase"""
    # Save to Firebase
    return update_admin_remarks_batch(data)


def save_awaiting_receipt(data: Dict[str, Any]) -> bool:
    """Save awaiting receipt data to Firebase"""
    # Save to Firebase
    return update_awaiting_receipt_batch(data)


def save_delivery_locations_temp(data: Dict[str, str]) -> bool:
    """Save temporary delivery locations data to Firebase"""
    # Save to Firebase
    return update_data("delivery_locations_temp", data)


def save_user_order_counts(data: Dict[str, int]) -> bool:
    """Save user order counts data to Firebase"""
    # Save to Firebase
    return update_data("user_order_counts", data)


def save_current_order_numbers(data: Dict[str, str]) -> bool:
    """Save current order numbers data to Firebase"""
    # Save to Firebase
    return update_data("current_order_numbers", data)


def save_user_data():
    """Save all user data to Firebase"""
    try:
        # Save user data to Firebase
        save_points(user_points)
        save_user_names(user_names)
        save_user_phone_numbers(user_phone_numbers)
        save_user_emails(user_emails)
        save_favorite_orders(favorite_orders)

        logger.info("User data saved successfully")
        return True
    except Exception as e:
        logger.error(f"Error in save_user_data: {e}")
        return False


def save_areas_data():
    """Save areas data to Firebase"""
    global areas_data
    # Save to Firebase
    return update_data("areas", areas_data)


def save_restaurants_data():
    """Save restaurants data to Firebase"""
    global restaurants_data
    # Save to Firebase
    return update_data("restaurants", restaurants_data)


def save_menus_data():
    """Save menus data to Firebase"""
    global menus_data
    # Save to Firebase
    return update_data("menus", menus_data)


def save_delivery_locations_data():
    """Save delivery locations data to Firebase"""
    global delivery_locations_data
    # Save to Firebase
    return update_data("delivery_locations", delivery_locations_data)


def save_delivery_fees_data():
    """Save delivery fees data to Firebase"""
    global delivery_fees_data
    # Save to Firebase
    return update_data("delivery_fees", delivery_fees_data)


def save_delivery_personnel():
    """Save delivery personnel data to Firebase"""
    global delivery_personnel
    # Save to Firebase
    return update_data("delivery_personnel", delivery_personnel)


def save_delivery_personnel_assignments(data: Dict[str, Dict[str, Any]] = None):
    """Save delivery personnel assignments to Firebase"""
    global delivery_personnel_assignments
    if data is not None:
        delivery_personnel_assignments.clear()
        delivery_personnel_assignments.update(data)
    # Save to Firebase
    return update_data("delivery_personnel_assignments", delivery_personnel_assignments)


def save_delivery_personnel_availability():
    """Save delivery personnel availability to Firebase"""
    global delivery_personnel_availability
    # Save to Firebase
    return update_data("delivery_personnel_availability", delivery_personnel_availability)


def save_delivery_personnel_capacity():
    """Save delivery personnel capacity to Firebase"""
    global delivery_personnel_capacity
    # Save to Firebase
    return update_data("delivery_personnel_capacity", delivery_personnel_capacity)


def save_delivery_personnel_zones():
    """Save delivery personnel zones to Firebase"""
    global delivery_personnel_zones
    # Save to Firebase
    return update_data("delivery_personnel_zones", delivery_personnel_zones)


def save_delivery_personnel_performance():
    """Save delivery personnel performance to Firebase"""
    global delivery_personnel_performance
    # Save to Firebase
    return update_data("delivery_personnel_performance", delivery_personnel_performance)


def save_delivery_personnel_earnings(data: Dict[str, Dict[str, Any]] = None):
    """Save delivery personnel earnings to Firebase with error handling"""
    try:
        global delivery_personnel_earnings
        if data is not None:
            # Validate data structure
            if not isinstance(data, dict):
                logger.error("Invalid data type for delivery personnel earnings: expected dict")
                return False

            delivery_personnel_earnings.clear()
            delivery_personnel_earnings.update(data)

        # Save to Firebase with error handling
        success = update_data("delivery_personnel_earnings", delivery_personnel_earnings)
        if not success:
            logger.error("Failed to save delivery personnel earnings to Firebase")
        return success

    except Exception as e:
        logger.error(f"Error saving delivery personnel earnings: {e}")
        return False


def _safe_write_json(file_path: str, data: Any) -> bool:
    """
    DEPRECATED: Legacy function kept for compatibility but now a no-op
    This function is deprecated as we now use Firebase for all data writing.
    """
    logger.warning(f"_safe_write_json() is deprecated - ignoring write to {file_path}, using Firebase instead")
    return True


# Area CRUD operations
def get_all_areas():
    """Get all areas"""
    global areas_data
    return areas_data.get("areas", [])


def get_area_by_id(area_id):
    """Get area by ID"""
    try:
        # Ensure area_id is an integer
        area_id = int(area_id)

        for area in get_all_areas():
            try:
                # Convert area id to integer for comparison
                if int(area["id"]) == area_id:
                    return area
            except (ValueError, TypeError):
                # Skip areas with non-integer IDs
                continue

        return None
    except (ValueError, TypeError):
        # If area_id can't be converted to int, try string comparison
        area_id_str = str(area_id)
        for area in get_all_areas():
            if str(area["id"]) == area_id_str:
                return area
        return None


def add_area(name):
    """Add a new area"""
    global areas_data
    areas = areas_data.get("areas", [])
    # Generate a new ID (max ID + 1)
    new_id = 1
    if areas:
        new_id = max(area["id"] for area in areas) + 1

    new_area = {"id": new_id, "name": name}

    areas.append(new_area)
    areas_data["areas"] = areas
    save_areas_data()
    return new_area


def update_area(area_id, name):
    """Update an existing area and synchronize favorite orders"""
    try:
        # Ensure area_id is an integer
        area_id = int(area_id)

        global areas_data
        for area in areas_data.get("areas", []):
            try:
                # Convert area id to integer for comparison
                if int(area["id"]) == area_id:
                    area["name"] = name
                    save_result = save_areas_data()

                    if save_result:
                        logger.info(f"Successfully updated area {area_id}")

                        # Trigger favorite orders synchronization after successful update
                        try:
                            from src.utils.favorite_orders_sync import sync_all_favorite_orders
                            sync_results = sync_all_favorite_orders()
                            if sync_results["orders_updated"] > 0:
                                logger.info(f"Synchronized {sync_results['orders_updated']} favorite orders after area update")
                        except ImportError:
                            logger.debug("Favorite orders sync module not available")
                        except Exception as sync_e:
                            logger.warning(f"Error syncing favorite orders after area update: {sync_e}")

                    return area
            except (ValueError, TypeError):
                # Skip areas with non-integer IDs
                continue

        return None
    except (ValueError, TypeError):
        # If area_id can't be converted to int, try string comparison
        area_id_str = str(area_id)
        for area in areas_data.get("areas", []):
            if str(area["id"]) == area_id_str:
                area["name"] = name
                save_result = save_areas_data()

                if save_result:
                    logger.info(f"Successfully updated area {area_id}")

                    # Trigger favorite orders synchronization after successful update
                    try:
                        from src.utils.favorite_orders_sync import sync_all_favorite_orders
                        sync_results = sync_all_favorite_orders()
                        if sync_results["orders_updated"] > 0:
                            logger.info(f"Synchronized {sync_results['orders_updated']} favorite orders after area update")
                    except ImportError:
                        logger.debug("Favorite orders sync module not available")
                    except Exception as sync_e:
                        logger.warning(f"Error syncing favorite orders after area update: {sync_e}")

                return area
        return None


def delete_area(area_id):
    """Delete an area and synchronize favorite orders"""
    try:
        # Ensure area_id is an integer
        area_id = int(area_id)

        global areas_data
        areas = areas_data.get("areas", [])
        for i, area in enumerate(areas):
            try:
                # Convert area id to integer for comparison
                if int(area["id"]) == area_id:
                    del areas[i]
                    areas_data["areas"] = areas
                    save_result = save_areas_data()

                    if save_result:
                        logger.info(f"Successfully deleted area {area_id}")

                        # Trigger favorite orders synchronization after successful deletion
                        try:
                            from src.utils.favorite_orders_sync import sync_all_favorite_orders
                            sync_results = sync_all_favorite_orders()
                            if sync_results["orders_updated"] > 0:
                                logger.info(f"Synchronized {sync_results['orders_updated']} favorite orders after area deletion")
                        except ImportError:
                            logger.debug("Favorite orders sync module not available")
                        except Exception as sync_e:
                            logger.warning(f"Error syncing favorite orders after area deletion: {sync_e}")

                    return True
            except (ValueError, TypeError):
                # Skip areas with non-integer IDs
                continue

        return False
    except (ValueError, TypeError):
        # If area_id can't be converted to int, try string comparison
        area_id_str = str(area_id)
        areas = areas_data.get("areas", [])
        for i, area in enumerate(areas):
            if str(area["id"]) == area_id_str:
                del areas[i]
                areas_data["areas"] = areas
                save_result = save_areas_data()

                if save_result:
                    logger.info(f"Successfully deleted area {area_id}")

                    # Trigger favorite orders synchronization after successful deletion
                    try:
                        from src.utils.favorite_orders_sync import sync_all_favorite_orders
                        sync_results = sync_all_favorite_orders()
                        if sync_results["orders_updated"] > 0:
                            logger.info(f"Synchronized {sync_results['orders_updated']} favorite orders after area deletion")
                    except ImportError:
                        logger.debug("Favorite orders sync module not available")
                    except Exception as sync_e:
                        logger.warning(f"Error syncing favorite orders after area deletion: {sync_e}")

                return True
        return False


# Restaurant CRUD operations
def get_all_restaurants():
    """Get all restaurants"""
    global restaurants_data
    return restaurants_data.get("restaurants", [])


def get_restaurants_by_area(area_id):
    """Get restaurants by area ID"""
    try:
        # Ensure area_id is an integer
        area_id = int(area_id)

        restaurants = []
        for restaurant in get_all_restaurants():
            try:
                # Convert restaurant area_id to integer for comparison
                restaurant_area_id = int(restaurant["area_id"])
                if restaurant_area_id == area_id:
                    restaurants.append(restaurant)
            except (ValueError, TypeError):
                # Skip restaurants with non-integer area_id
                continue

        return restaurants
    except (ValueError, TypeError):
        # If area_id can't be converted to int, try string comparison
        area_id_str = str(area_id)
        restaurants = []
        for restaurant in get_all_restaurants():
            if str(restaurant["area_id"]) == area_id_str:
                restaurants.append(restaurant)
        return restaurants


def get_restaurant_by_id(restaurant_id):
    """Get restaurant by ID"""
    try:
        # Ensure restaurant_id is an integer
        restaurant_id = int(restaurant_id)

        for restaurant in get_all_restaurants():
            try:
                # Convert restaurant id to integer for comparison
                if int(restaurant["id"]) == restaurant_id:
                    return restaurant
            except (ValueError, TypeError):
                # Skip restaurants with non-integer IDs
                continue

        return None
    except (ValueError, TypeError):
        # If restaurant_id can't be converted to int, try string comparison
        restaurant_id_str = str(restaurant_id)
        for restaurant in get_all_restaurants():
            if str(restaurant["id"]) == restaurant_id_str:
                return restaurant
        return None


def add_restaurant(name, area_id):
    """Add a new restaurant"""
    global restaurants_data
    restaurants = restaurants_data.get("restaurants", [])
    # Generate a new ID (max ID + 1)
    new_id = 1
    if restaurants:
        new_id = max(restaurant["id"] for restaurant in restaurants) + 1

    new_restaurant = {"id": new_id, "name": name, "area_id": area_id}

    restaurants.append(new_restaurant)
    restaurants_data["restaurants"] = restaurants
    save_restaurants_data()
    return new_restaurant


def update_restaurant(restaurant_id, name=None, area_id=None):
    """Update an existing restaurant and synchronize favorite orders"""
    try:
        # Ensure restaurant_id is an integer
        restaurant_id = int(restaurant_id)

        global restaurants_data
        for restaurant in restaurants_data.get("restaurants", []):
            try:
                # Convert restaurant id to integer for comparison
                if int(restaurant["id"]) == restaurant_id:
                    if name is not None:
                        restaurant["name"] = name
                    if area_id is not None:
                        restaurant["area_id"] = area_id
                    save_result = save_restaurants_data()

                    if save_result:
                        logger.info(f"Successfully updated restaurant {restaurant_id}")

                        # Trigger favorite orders synchronization after successful update
                        try:
                            from src.utils.favorite_orders_sync import sync_all_favorite_orders
                            sync_results = sync_all_favorite_orders()
                            if sync_results["orders_updated"] > 0:
                                logger.info(f"Synchronized {sync_results['orders_updated']} favorite orders after restaurant update")
                        except ImportError:
                            logger.debug("Favorite orders sync module not available")
                        except Exception as sync_e:
                            logger.warning(f"Error syncing favorite orders after restaurant update: {sync_e}")

                    return restaurant
            except (ValueError, TypeError):
                # Skip restaurants with non-integer IDs
                continue

        return None
    except (ValueError, TypeError):
        # If restaurant_id can't be converted to int, try string comparison
        restaurant_id_str = str(restaurant_id)
        for restaurant in restaurants_data.get("restaurants", []):
            if str(restaurant["id"]) == restaurant_id_str:
                if name is not None:
                    restaurant["name"] = name
                if area_id is not None:
                    restaurant["area_id"] = area_id
                save_result = save_restaurants_data()

                if save_result:
                    logger.info(f"Successfully updated restaurant {restaurant_id}")

                    # Trigger favorite orders synchronization after successful update
                    try:
                        from src.utils.favorite_orders_sync import sync_all_favorite_orders
                        sync_results = sync_all_favorite_orders()
                        if sync_results["orders_updated"] > 0:
                            logger.info(f"Synchronized {sync_results['orders_updated']} favorite orders after restaurant update")
                    except ImportError:
                        logger.debug("Favorite orders sync module not available")
                    except Exception as sync_e:
                        logger.warning(f"Error syncing favorite orders after restaurant update: {sync_e}")

                return restaurant
        return None


def delete_restaurant(restaurant_id):
    """Delete a restaurant and synchronize favorite orders"""
    try:
        # Ensure restaurant_id is an integer
        restaurant_id = int(restaurant_id)

        global restaurants_data
        restaurants = restaurants_data.get("restaurants", [])
        for i, restaurant in enumerate(restaurants):
            try:
                # Convert restaurant id to integer for comparison
                if int(restaurant["id"]) == restaurant_id:
                    del restaurants[i]
                    restaurants_data["restaurants"] = restaurants
                    save_result = save_restaurants_data()

                    if save_result:
                        logger.info(f"Successfully deleted restaurant {restaurant_id}")

                        # Trigger favorite orders synchronization after successful deletion
                        try:
                            from src.utils.favorite_orders_sync import sync_all_favorite_orders
                            sync_results = sync_all_favorite_orders()
                            if sync_results["orders_updated"] > 0:
                                logger.info(f"Synchronized {sync_results['orders_updated']} favorite orders after restaurant deletion")
                        except ImportError:
                            logger.debug("Favorite orders sync module not available")
                        except Exception as sync_e:
                            logger.warning(f"Error syncing favorite orders after restaurant deletion: {sync_e}")

                    return True
            except (ValueError, TypeError):
                # Skip restaurants with non-integer IDs
                continue

        return False
    except (ValueError, TypeError):
        # If restaurant_id can't be converted to int, try string comparison
        restaurant_id_str = str(restaurant_id)
        restaurants = restaurants_data.get("restaurants", [])
        for i, restaurant in enumerate(restaurants):
            if str(restaurant["id"]) == restaurant_id_str:
                del restaurants[i]
                restaurants_data["restaurants"] = restaurants
                save_result = save_restaurants_data()

                if save_result:
                    logger.info(f"Successfully deleted restaurant {restaurant_id}")

                    # Trigger favorite orders synchronization after successful deletion
                    try:
                        from src.utils.favorite_orders_sync import sync_all_favorite_orders
                        sync_results = sync_all_favorite_orders()
                        if sync_results["orders_updated"] > 0:
                            logger.info(f"Synchronized {sync_results['orders_updated']} favorite orders after restaurant deletion")
                    except ImportError:
                        logger.debug("Favorite orders sync module not available")
                    except Exception as sync_e:
                        logger.warning(f"Error syncing favorite orders after restaurant deletion: {sync_e}")

                return True
        return False


# Menu CRUD operations
def get_default_menu_items():
    """Get default menu items"""
    global menus_data
    return menus_data.get("default_menu_items", [])


def get_restaurant_menu(restaurant_id):
    """Get menu for a specific restaurant"""
    global menus_data
    restaurant_id_str = str(restaurant_id)

    # Debug logging to check menus_data structure
    logger.debug(f"menus_data type: {type(menus_data)}")
    logger.debug(f"menus_data contents: {menus_data}")

    # Ensure menus_data is a dictionary and has restaurant_menus as a dictionary
    if not isinstance(menus_data, dict):
        logger.error(f"menus_data is not a dictionary: {type(menus_data)}")
        menus_data = {"default_menu_items": [], "restaurant_menus": {}}

    # Initialize restaurant_menus if it doesn't exist or isn't a dictionary
    if "restaurant_menus" not in menus_data or not isinstance(
        menus_data["restaurant_menus"], dict
    ):
        logger.warning(f"restaurant_menus missing or not a dictionary. Initializing...")
        menus_data["restaurant_menus"] = {}

    restaurant_menus = menus_data.get("restaurant_menus", {})
    logger.debug(f"restaurant_menus type: {type(restaurant_menus)}")

    # Handle Firebase Realtime Database structure
    # Check for new format with restaurant_ prefix (e.g., "restaurant_1")
    restaurant_key = f"restaurant_{restaurant_id}"

    if isinstance(restaurant_menus, dict):
        # Try new format first (restaurant_1, restaurant_2, etc.)
        if restaurant_key in restaurant_menus and restaurant_menus[restaurant_key]:
            logger.info(
                f"Found existing menu for restaurant {restaurant_id} with {len(restaurant_menus[restaurant_key])} items"
            )
            return restaurant_menus[restaurant_key]
        # Try old format (1, 2, etc.)
        elif restaurant_id_str in restaurant_menus and restaurant_menus[restaurant_id_str]:
            logger.info(
                f"Found existing menu for restaurant {restaurant_id} with {len(restaurant_menus[restaurant_id_str])} items"
            )
            return restaurant_menus[restaurant_id_str]
    elif isinstance(restaurant_menus, list):
        # Handle Firebase array format (numeric keys converted to array indices)
        try:
            restaurant_id_int = int(restaurant_id)
            if restaurant_id_int < len(restaurant_menus) and restaurant_menus[restaurant_id_int]:
                logger.info(
                    f"Found existing menu for restaurant {restaurant_id} with {len(restaurant_menus[restaurant_id_int])} items"
                )
                return restaurant_menus[restaurant_id_int]
        except (ValueError, TypeError, IndexError) as e:
            logger.error(f"Error accessing menu for restaurant {restaurant_id} from list: {e}")

    # No menu found in Firebase - fall back to sample data
    logger.warning(f"No menu found in Firebase for restaurant {restaurant_id}")
    logger.info("Falling back to hardcoded sample data")

    # Import sample menu data as fallback
    try:
        from src.data.menus import menus as sample_menus
        restaurant_id_int = int(restaurant_id)
        sample_menu = None

        if restaurant_id_int in sample_menus:
            sample_menu = sample_menus[restaurant_id_int]
            logger.info(f"Using sample menu for restaurant {restaurant_id} with {len(sample_menu)} items")
        else:
            # Use default sample menu items
            from src.data.menus import default_menu_items
            sample_menu = default_menu_items
            logger.info(f"Using default sample menu for restaurant {restaurant_id} with {len(sample_menu)} items")

        # Store the sample menu in menus_data so update operations can find it
        if sample_menu:
            if "restaurant_menus" not in menus_data:
                menus_data["restaurant_menus"] = {}

            # Store using the same key format that update operations will look for
            restaurant_key = f"restaurant_{restaurant_id}"
            menus_data["restaurant_menus"][restaurant_key] = sample_menu.copy()
            logger.debug(f"Stored sample menu in menus_data under key '{restaurant_key}'")

        return sample_menu

    except (ImportError, ValueError, TypeError) as e:
        logger.error(f"Error loading sample menu data: {e}")
        return []


def add_menu_item(restaurant_id, name, price):
    """Add a menu item to a restaurant"""
    global menus_data
    restaurant_id_str = str(restaurant_id)
    restaurant_menus = menus_data.get("restaurant_menus", {})

    # Handle both formats like get_restaurant_menu does
    restaurant_key = f"restaurant_{restaurant_id}"

    # Determine which format to use - prefer existing format if available
    if restaurant_key in restaurant_menus:
        # Use new format (restaurant_1, restaurant_2, etc.)
        if restaurant_key not in restaurant_menus:
            restaurant_menus[restaurant_key] = []
        menu_items = restaurant_menus[restaurant_key]
    else:
        # Use old format (1, 2, etc.) or create new format if neither exists
        if restaurant_id_str not in restaurant_menus:
            # Default to new format for new restaurants
            restaurant_menus[restaurant_key] = []
            menu_items = restaurant_menus[restaurant_key]
        else:
            menu_items = restaurant_menus[restaurant_id_str]

    # Generate a new ID (max ID + 1)
    new_id = 1
    if menu_items:
        new_id = max(item["id"] for item in menu_items) + 1

    new_item = {"id": new_id, "name": name, "price": price}

    menu_items.append(new_item)
    menus_data["restaurant_menus"] = restaurant_menus
    save_menus_data()
    return new_item


def update_menu_item(restaurant_id, item_id, name=None, price=None):
    """Update a menu item and synchronize favorite orders"""
    global menus_data
    restaurant_id_str = str(restaurant_id)
    restaurant_menus = menus_data.get("restaurant_menus", {})

    # Handle both formats like get_restaurant_menu does
    restaurant_key = f"restaurant_{restaurant_id}"
    menu_items = None

    logger.info(f"Updating menu item - restaurant_id: {restaurant_id}, item_id: {item_id}, name: {name}, price: {price}")
    logger.debug(f"Available restaurant keys: {list(restaurant_menus.keys())}")

    # Try new format first (restaurant_1, restaurant_2, etc.)
    if restaurant_key in restaurant_menus and restaurant_menus[restaurant_key]:
        menu_items = restaurant_menus[restaurant_key]
        logger.debug(f"Found menu items using new format key '{restaurant_key}' with {len(menu_items)} items")
    # Try old format (1, 2, etc.)
    elif restaurant_id_str in restaurant_menus and restaurant_menus[restaurant_id_str]:
        menu_items = restaurant_menus[restaurant_id_str]
        logger.debug(f"Found menu items using old format key '{restaurant_id_str}' with {len(menu_items)} items")

    if not menu_items:
        logger.error(f"No menu items found for restaurant {restaurant_id} (tried keys: '{restaurant_key}', '{restaurant_id_str}')")
        logger.error(f"Available restaurant data: {list(restaurant_menus.keys())}")
        return None

    # Find the item to update
    item_found = False
    for item in menu_items:
        if item["id"] == item_id:
            item_found = True
            logger.debug(f"Found menu item to update: {item}")

            # Store original values for rollback if needed
            original_name = item.get("name")
            original_price = item.get("price")

            # Update the item
            if name is not None:
                item["name"] = name
            if price is not None:
                item["price"] = price

            logger.info(f"Attempting to save updated menu item to Firebase...")

            try:
                save_result = save_menus_data()
                logger.info(f"Firebase save result: {save_result}")

                if save_result:
                    logger.info(f"Successfully updated menu item {item_id} for restaurant {restaurant_id}")

                    # Trigger favorite orders synchronization after successful update
                    try:
                        from src.utils.favorite_orders_sync import sync_all_favorite_orders
                        sync_results = sync_all_favorite_orders()
                        if sync_results["orders_updated"] > 0:
                            logger.info(f"Synchronized {sync_results['orders_updated']} favorite orders after menu item update")
                    except ImportError:
                        logger.debug("Favorite orders sync module not available")
                    except Exception as sync_e:
                        logger.warning(f"Error syncing favorite orders after menu item update: {sync_e}")

                    return item
                else:
                    logger.error(f"Failed to save menu data to Firebase after updating item {item_id}")
                    # Rollback changes
                    if name is not None:
                        item["name"] = original_name
                    if price is not None:
                        item["price"] = original_price
                    logger.info(f"Rolled back changes to menu item {item_id}")
                    return None

            except Exception as e:
                logger.error(f"Exception occurred while saving menu data: {e}", exc_info=True)
                # Rollback changes
                if name is not None:
                    item["name"] = original_name
                if price is not None:
                    item["price"] = original_price
                logger.info(f"Rolled back changes to menu item {item_id} due to exception")
                return None

    if not item_found:
        logger.error(f"Menu item with ID {item_id} not found in restaurant {restaurant_id}")
        logger.debug(f"Available item IDs: {[item.get('id') for item in menu_items]}")

    return None


def delete_menu_item(restaurant_id, item_id):
    """Delete a menu item and synchronize favorite orders"""
    global menus_data
    restaurant_id_str = str(restaurant_id)
    restaurant_menus = menus_data.get("restaurant_menus", {})

    # Handle both formats like get_restaurant_menu does
    restaurant_key = f"restaurant_{restaurant_id}"
    menu_items = None

    # Try new format first (restaurant_1, restaurant_2, etc.)
    if restaurant_key in restaurant_menus and restaurant_menus[restaurant_key]:
        menu_items = restaurant_menus[restaurant_key]
    # Try old format (1, 2, etc.)
    elif restaurant_id_str in restaurant_menus and restaurant_menus[restaurant_id_str]:
        menu_items = restaurant_menus[restaurant_id_str]

    if not menu_items:
        return False

    for i, item in enumerate(menu_items):
        if item["id"] == item_id:
            del menu_items[i]
            save_result = save_menus_data()

            if save_result:
                logger.info(f"Successfully deleted menu item {item_id} for restaurant {restaurant_id}")

                # Trigger favorite orders synchronization after successful deletion
                try:
                    from src.utils.favorite_orders_sync import sync_all_favorite_orders
                    sync_results = sync_all_favorite_orders()
                    if sync_results["orders_updated"] > 0:
                        logger.info(f"Synchronized {sync_results['orders_updated']} favorite orders after menu item deletion")
                except ImportError:
                    logger.debug("Favorite orders sync module not available")
                except Exception as sync_e:
                    logger.warning(f"Error syncing favorite orders after menu item deletion: {sync_e}")

                return True
            else:
                logger.error(f"Failed to save menu data after deleting item {item_id}")
                return False

    return False


# Delivery Location CRUD operations
def get_all_delivery_locations():
    """Get all delivery locations"""
    global delivery_locations_data
    return delivery_locations_data.get("delivery_locations", [])


def get_delivery_location_by_id(location_id):
    """Get delivery location by ID"""
    try:
        # Ensure location_id is an integer
        location_id = int(location_id)

        for location in get_all_delivery_locations():
            try:
                # Convert location id to integer for comparison
                if int(location["id"]) == location_id:
                    return location
            except (ValueError, TypeError):
                # Skip locations with non-integer IDs
                continue

        return None
    except (ValueError, TypeError):
        # If location_id can't be converted to int, try string comparison
        location_id_str = str(location_id)
        for location in get_all_delivery_locations():
            if str(location["id"]) == location_id_str:
                return location
        return None


def add_delivery_location(name):
    """Add a new delivery location"""
    global delivery_locations_data
    locations = delivery_locations_data.get("delivery_locations", [])
    # Generate a new ID (max ID + 1)
    new_id = 1
    if locations:
        new_id = max(location["id"] for location in locations) + 1

    new_location = {"id": new_id, "name": name}

    locations.append(new_location)
    delivery_locations_data["delivery_locations"] = locations
    save_delivery_locations_data()
    return new_location


def update_delivery_location(location_id, name):
    """Update an existing delivery location and synchronize favorite orders"""
    try:
        # Ensure location_id is an integer
        location_id = int(location_id)

        global delivery_locations_data
        for location in delivery_locations_data.get("delivery_locations", []):
            try:
                # Convert location id to integer for comparison
                if int(location["id"]) == location_id:
                    location["name"] = name
                    save_result = save_delivery_locations_data()

                    if save_result:
                        logger.info(f"Successfully updated delivery location {location_id}")

                        # Trigger favorite orders synchronization after successful update
                        try:
                            from src.utils.favorite_orders_sync import sync_all_favorite_orders
                            sync_results = sync_all_favorite_orders()
                            if sync_results["orders_updated"] > 0:
                                logger.info(f"Synchronized {sync_results['orders_updated']} favorite orders after delivery location update")
                        except ImportError:
                            logger.debug("Favorite orders sync module not available")
                        except Exception as sync_e:
                            logger.warning(f"Error syncing favorite orders after delivery location update: {sync_e}")

                    return location
            except (ValueError, TypeError):
                # Skip locations with non-integer IDs
                continue

        return None
    except (ValueError, TypeError):
        # If location_id can't be converted to int, try string comparison
        location_id_str = str(location_id)
        for location in delivery_locations_data.get("delivery_locations", []):
            if str(location["id"]) == location_id_str:
                location["name"] = name
                save_result = save_delivery_locations_data()

                if save_result:
                    logger.info(f"Successfully updated delivery location {location_id}")

                    # Trigger favorite orders synchronization after successful update
                    try:
                        from src.utils.favorite_orders_sync import sync_all_favorite_orders
                        sync_results = sync_all_favorite_orders()
                        if sync_results["orders_updated"] > 0:
                            logger.info(f"Synchronized {sync_results['orders_updated']} favorite orders after delivery location update")
                    except ImportError:
                        logger.debug("Favorite orders sync module not available")
                    except Exception as sync_e:
                        logger.warning(f"Error syncing favorite orders after delivery location update: {sync_e}")

                return location
        return None


def delete_delivery_location(location_id):
    """Delete a delivery location and synchronize favorite orders"""
    try:
        # Ensure location_id is an integer
        location_id = int(location_id)

        global delivery_locations_data
        locations = delivery_locations_data.get("delivery_locations", [])
        for i, location in enumerate(locations):
            try:
                # Convert location id to integer for comparison
                if int(location["id"]) == location_id:
                    del locations[i]
                    delivery_locations_data["delivery_locations"] = locations
                    save_result = save_delivery_locations_data()

                    if save_result:
                        logger.info(f"Successfully deleted delivery location {location_id}")

                        # Trigger favorite orders synchronization after successful deletion
                        try:
                            from src.utils.favorite_orders_sync import sync_all_favorite_orders
                            sync_results = sync_all_favorite_orders()
                            if sync_results["orders_updated"] > 0:
                                logger.info(f"Synchronized {sync_results['orders_updated']} favorite orders after delivery location deletion")
                        except ImportError:
                            logger.debug("Favorite orders sync module not available")
                        except Exception as sync_e:
                            logger.warning(f"Error syncing favorite orders after delivery location deletion: {sync_e}")

                    return True
            except (ValueError, TypeError):
                # Skip locations with non-integer IDs
                continue

        return False
    except (ValueError, TypeError):
        # If location_id can't be converted to int, try string comparison
        location_id_str = str(location_id)
        locations = delivery_locations_data.get("delivery_locations", [])
        for i, location in enumerate(locations):
            if str(location["id"]) == location_id_str:
                del locations[i]
                delivery_locations_data["delivery_locations"] = locations
                save_result = save_delivery_locations_data()

                if save_result:
                    logger.info(f"Successfully deleted delivery location {location_id}")

                    # Trigger favorite orders synchronization after successful deletion
                    try:
                        from src.utils.favorite_orders_sync import sync_all_favorite_orders
                        sync_results = sync_all_favorite_orders()
                        if sync_results["orders_updated"] > 0:
                            logger.info(f"Synchronized {sync_results['orders_updated']} favorite orders after delivery location deletion")
                    except ImportError:
                        logger.debug("Favorite orders sync module not available")
                    except Exception as sync_e:
                        logger.warning(f"Error syncing favorite orders after delivery location deletion: {sync_e}")

                return True
        return False


# Delivery Fee CRUD operations
def get_all_delivery_fees():
    """Get all delivery fees"""
    global delivery_fees_data
    return delivery_fees_data.get("delivery_fees", [])


def get_delivery_fee(area_id, location_id):
    """Get delivery fee for a specific area and location"""
    try:
        # Ensure both IDs are integers
        area_id = int(area_id)
        location_id = int(location_id)

        logger.info(
            f"Looking for delivery fee - Area ID: {area_id} ({type(area_id)}), Location ID: {location_id} ({type(location_id)})"
        )
        all_fees = get_all_delivery_fees()
        logger.info(f"Total delivery fees available: {len(all_fees)}")

        for fee in all_fees:
            # Convert fee IDs to integers for comparison
            fee_area_id = int(fee["area_id"])
            fee_location_id = int(fee["location_id"])

            # This verbose logging is now filtered by LogFilter class
            logger.info(
                f"Checking fee: area_id={fee_area_id}, location_id={fee_location_id}, fee={fee['fee']}"
            )

            if fee_area_id == area_id and fee_location_id == location_id:
                # Use the new logger method for a cleaner summary
                logger.log_fee_summary(area_id, location_id, fee["fee"])
                return fee["fee"]

        logger.warning(f"No fee found for area_id={area_id}, location_id={location_id}")
        return 0  # Default fee
    except (ValueError, TypeError) as e:
        logger.error(f"Error in get_delivery_fee: {e}")
        logger.error(
            f"Arguments: area_id={area_id} ({type(area_id)}), location_id={location_id} ({type(location_id)})"
        )
        return 0  # Return default fee on error


def add_delivery_fee(area_id, location_id, fee):
    """Add or update a delivery fee and synchronize favorite orders"""
    try:
        # Ensure IDs are integers
        area_id_int = int(area_id)
        location_id_int = int(location_id)

        global delivery_fees_data
        fees = delivery_fees_data.get("delivery_fees", [])

        # Check if fee already exists
        for existing_fee in fees:
            try:
                # Convert fee IDs to integers for comparison
                fee_area_id = int(existing_fee["area_id"])
                fee_location_id = int(existing_fee["location_id"])

                if fee_area_id == area_id_int and fee_location_id == location_id_int:
                    existing_fee["fee"] = fee
                    save_result = save_delivery_fees_data()

                    if save_result:
                        logger.info(f"Successfully updated delivery fee for area {area_id} and location {location_id}")

                        # Trigger favorite orders synchronization after successful update
                        try:
                            from src.utils.favorite_orders_sync import sync_all_favorite_orders
                            sync_results = sync_all_favorite_orders()
                            if sync_results["orders_updated"] > 0:
                                logger.info(f"Synchronized {sync_results['orders_updated']} favorite orders after delivery fee update")
                        except ImportError:
                            logger.debug("Favorite orders sync module not available")
                        except Exception as sync_e:
                            logger.warning(f"Error syncing favorite orders after delivery fee update: {sync_e}")

                    return existing_fee
            except (ValueError, TypeError):
                # Skip fees with non-integer IDs
                continue

        # Add new fee - store original values to maintain data type consistency
        new_fee = {"area_id": area_id, "location_id": location_id, "fee": fee}

        fees.append(new_fee)
        delivery_fees_data["delivery_fees"] = fees
        save_result = save_delivery_fees_data()

        if save_result:
            logger.info(f"Successfully added delivery fee for area {area_id} and location {location_id}")

            # Trigger favorite orders synchronization after successful addition
            try:
                from src.utils.favorite_orders_sync import sync_all_favorite_orders
                sync_results = sync_all_favorite_orders()
                if sync_results["orders_updated"] > 0:
                    logger.info(f"Synchronized {sync_results['orders_updated']} favorite orders after delivery fee addition")
            except ImportError:
                logger.debug("Favorite orders sync module not available")
            except Exception as sync_e:
                logger.warning(f"Error syncing favorite orders after delivery fee addition: {sync_e}")

        return new_fee
    except (ValueError, TypeError):
        # If IDs can't be converted to int, use string comparison
        area_id_str = str(area_id)
        location_id_str = str(location_id)

        for existing_fee in fees:
            if (
                str(existing_fee["area_id"]) == area_id_str
                and str(existing_fee["location_id"]) == location_id_str
            ):
                existing_fee["fee"] = fee
                save_result = save_delivery_fees_data()

                if save_result:
                    logger.info(f"Successfully updated delivery fee for area {area_id} and location {location_id}")

                    # Trigger favorite orders synchronization after successful update
                    try:
                        from src.utils.favorite_orders_sync import sync_all_favorite_orders
                        sync_results = sync_all_favorite_orders()
                        if sync_results["orders_updated"] > 0:
                            logger.info(f"Synchronized {sync_results['orders_updated']} favorite orders after delivery fee update")
                    except ImportError:
                        logger.debug("Favorite orders sync module not available")
                    except Exception as sync_e:
                        logger.warning(f"Error syncing favorite orders after delivery fee update: {sync_e}")

                return existing_fee

        # Add new fee with string IDs
        new_fee = {"area_id": area_id, "location_id": location_id, "fee": fee}

        fees.append(new_fee)
        delivery_fees_data["delivery_fees"] = fees
        save_result = save_delivery_fees_data()

        if save_result:
            logger.info(f"Successfully added delivery fee for area {area_id} and location {location_id}")

            # Trigger favorite orders synchronization after successful addition
            try:
                from src.utils.favorite_orders_sync import sync_all_favorite_orders
                sync_results = sync_all_favorite_orders()
                if sync_results["orders_updated"] > 0:
                    logger.info(f"Synchronized {sync_results['orders_updated']} favorite orders after delivery fee addition")
            except ImportError:
                logger.debug("Favorite orders sync module not available")
            except Exception as sync_e:
                logger.warning(f"Error syncing favorite orders after delivery fee addition: {sync_e}")

        return new_fee


def delete_delivery_fee(area_id, location_id):
    """Delete a delivery fee and synchronize favorite orders"""
    try:
        # Ensure IDs are integers
        area_id = int(area_id)
        location_id = int(location_id)

        global delivery_fees_data
        fees = delivery_fees_data.get("delivery_fees", [])
        for i, fee in enumerate(fees):
            try:
                # Convert fee IDs to integers for comparison
                fee_area_id = int(fee["area_id"])
                fee_location_id = int(fee["location_id"])

                if fee_area_id == area_id and fee_location_id == location_id:
                    del fees[i]
                    delivery_fees_data["delivery_fees"] = fees
                    save_result = save_delivery_fees_data()

                    if save_result:
                        logger.info(f"Successfully deleted delivery fee for area {area_id} and location {location_id}")

                        # Trigger favorite orders synchronization after successful deletion
                        try:
                            from src.utils.favorite_orders_sync import sync_all_favorite_orders
                            sync_results = sync_all_favorite_orders()
                            if sync_results["orders_updated"] > 0:
                                logger.info(f"Synchronized {sync_results['orders_updated']} favorite orders after delivery fee deletion")
                        except ImportError:
                            logger.debug("Favorite orders sync module not available")
                        except Exception as sync_e:
                            logger.warning(f"Error syncing favorite orders after delivery fee deletion: {sync_e}")

                    return True
            except (ValueError, TypeError):
                # Skip fees with non-integer IDs
                continue

        return False
    except (ValueError, TypeError):
        # If IDs can't be converted to int, try string comparison
        area_id_str = str(area_id)
        location_id_str = str(location_id)
        fees = delivery_fees_data.get("delivery_fees", [])

        for i, fee in enumerate(fees):
            if (
                str(fee["area_id"]) == area_id_str
                and str(fee["location_id"]) == location_id_str
            ):
                del fees[i]
                delivery_fees_data["delivery_fees"] = fees
                save_result = save_delivery_fees_data()

                if save_result:
                    logger.info(f"Successfully deleted delivery fee for area {area_id} and location {location_id}")

                    # Trigger favorite orders synchronization after successful deletion
                    try:
                        from src.utils.favorite_orders_sync import sync_all_favorite_orders
                        sync_results = sync_all_favorite_orders()
                        if sync_results["orders_updated"] > 0:
                            logger.info(f"Synchronized {sync_results['orders_updated']} favorite orders after delivery fee deletion")
                    except ImportError:
                        logger.debug("Favorite orders sync module not available")
                    except Exception as sync_e:
                        logger.warning(f"Error syncing favorite orders after delivery fee deletion: {sync_e}")

                return True
        return False


# Alias for compatibility with existing code
def save_all_user_data():
    """Alias for save_user_data"""
    return save_user_data()


def sync_all_favorite_orders_periodic():
    """Perform periodic synchronization of all favorite orders"""
    try:
        logger.info("🔄 Starting periodic favorite orders synchronization...")

        from src.utils.favorite_orders_sync import sync_all_favorite_orders
        sync_results = sync_all_favorite_orders()

        if sync_results["success"]:
            if sync_results["orders_updated"] > 0:
                logger.info(f"✅ Periodic sync completed: {sync_results['orders_updated']} orders updated across {sync_results['users_processed']} users")
            else:
                logger.debug(f"✅ Periodic sync completed: No updates needed for {sync_results['users_processed']} users")
        else:
            logger.error(f"❌ Periodic sync failed with {len(sync_results['errors'])} errors")

        return sync_results

    except ImportError:
        logger.debug("Favorite orders sync module not available for periodic sync")
        return {"success": False, "error": "Sync module not available"}
    except Exception as e:
        logger.error(f"Error in periodic favorite orders sync: {e}")
        return {"success": False, "error": str(e)}


def validate_favorite_orders_persistence():
    """Validate that favorite orders are properly persisted in Firebase"""
    try:
        logger.info("🔍 Validating favorite orders persistence...")

        # Get local favorite orders count
        local_users = len(favorite_orders)
        local_total_orders = sum(len(user_favorites) for user_favorites in favorite_orders.values())

        # Get Firebase favorite orders count
        from src.firebase_db import get_favorite_orders as get_firebase_favorite_orders
        firebase_favorites = get_firebase_favorite_orders()
        firebase_users = len(firebase_favorites)
        firebase_total_orders = sum(len(user_favorites) for user_favorites in firebase_favorites.values())

        # Compare counts
        persistence_valid = (local_users == firebase_users and local_total_orders == firebase_total_orders)

        if persistence_valid:
            logger.info(f"✅ Favorite orders persistence validated: {local_users} users, {local_total_orders} orders")
        else:
            logger.warning(f"⚠️ Favorite orders persistence mismatch:")
            logger.warning(f"   Local: {local_users} users, {local_total_orders} orders")
            logger.warning(f"   Firebase: {firebase_users} users, {firebase_total_orders} orders")

            # Attempt to sync local data to Firebase
            try:
                from src.firebase_db import update_favorite_orders_batch
                sync_success = update_favorite_orders_batch(favorite_orders)
                if sync_success:
                    logger.info("✅ Successfully synced local favorite orders to Firebase")
                    persistence_valid = True
                else:
                    logger.error("❌ Failed to sync local favorite orders to Firebase")
            except Exception as sync_e:
                logger.error(f"❌ Error syncing favorite orders to Firebase: {sync_e}")

        return {
            "valid": persistence_valid,
            "local_users": local_users,
            "local_orders": local_total_orders,
            "firebase_users": firebase_users,
            "firebase_orders": firebase_total_orders
        }

    except Exception as e:
        logger.error(f"Error validating favorite orders persistence: {e}")
        return {
            "valid": False,
            "error": str(e),
            "local_users": len(favorite_orders),
            "local_orders": sum(len(user_favorites) for user_favorites in favorite_orders.values()),
            "firebase_users": 0,
            "firebase_orders": 0
        }


def ensure_favorite_orders_persistence():
    """Ensure favorite orders are properly persisted and synchronized"""
    try:
        logger.info("🔧 Ensuring favorite orders persistence...")

        # Validate current persistence
        validation_result = validate_favorite_orders_persistence()

        if validation_result["valid"]:
            logger.info("✅ Favorite orders persistence is valid")
            return True

        # If validation failed, attempt to fix
        logger.warning("⚠️ Favorite orders persistence issues detected, attempting to fix...")

        # Reload from Firebase
        try:
            from src.firebase_db import get_favorite_orders as get_firebase_favorite_orders
            firebase_favorites = get_firebase_favorite_orders()

            # Update local cache
            favorite_orders.clear()
            favorite_orders.update(firebase_favorites)

            logger.info(f"✅ Reloaded {len(firebase_favorites)} users' favorite orders from Firebase")

            # Validate again
            validation_result = validate_favorite_orders_persistence()
            if validation_result["valid"]:
                logger.info("✅ Favorite orders persistence fixed successfully")
                return True
            else:
                logger.error("❌ Failed to fix favorite orders persistence")
                return False

        except Exception as reload_e:
            logger.error(f"❌ Error reloading favorite orders from Firebase: {reload_e}")
            return False

    except Exception as e:
        logger.error(f"Error ensuring favorite orders persistence: {e}")
        return False


def clean_up_order_data(user_id: int, order_number: Optional[str]):
    """Clean up all order-related data"""
    logger.info(f"Starting cleanup for order #{order_number}, user ID: {user_id}")

    # Convert user_id to string for safer dictionary operations
    user_id_str = str(user_id) if user_id is not None else None

    # Ensure order_number is valid
    if order_number is None:
        order_number = ""
        logger.warning("clean_up_order_data called with None order_number")

    # Track which operations succeeded and failed
    cleanup_results = {
        "pending_admin_reviews": False,
        "admin_remarks": False,
        "orders": False,
        "order_status": False,
        "awaiting_receipt": False,
        "current_order_numbers": False,
    }

    # Define a helper function to safely remove items from dictionaries
    def safe_dict_remove(data_dict, key, dict_name):
        try:
            if data_dict is not None and key in data_dict:
                del data_dict[key]
                cleanup_results[dict_name] = True
                logger.info(f"Removed user {key} from {dict_name} dict")
                return True
            return False
        except Exception as e:
            logger.error(f"Error removing {key} from {dict_name}: {e}")
            return False

    # Try to remove order from pending_admin_reviews
    try:
        if order_number and order_number in pending_admin_reviews:
            del pending_admin_reviews[order_number]
            cleanup_results["pending_admin_reviews"] = True
            logger.info(f"Removed order #{order_number} from pending_admin_reviews")
    except Exception as e:
        logger.error(f"Error removing order from pending_admin_reviews: {e}")

    # Try to remove order from admin_remarks
    try:
        if order_number and order_number in admin_remarks:
            del admin_remarks[order_number]
            cleanup_results["admin_remarks"] = True
            logger.info(f"Removed order #{order_number} from admin_remarks")
    except Exception as e:
        logger.error(f"Error removing order from admin_remarks: {e}")

    # Remove user's data from the remaining dictionaries
    if user_id is not None:
        # Remove from orders
        safe_dict_remove(orders, user_id, "orders")

        # Remove from order_status
        safe_dict_remove(order_status, user_id, "order_status")

        # Remove from awaiting_receipt
        safe_dict_remove(awaiting_receipt, user_id, "awaiting_receipt")

        # Remove from current_order_numbers
        safe_dict_remove(current_order_numbers, user_id, "current_order_numbers")

    # Now persist changes to disk
    try:
        # Only save dicts that were modified
        dicts_to_save = []

        if cleanup_results["pending_admin_reviews"]:
            dicts_to_save.append(("pending_admin_reviews", save_pending_admin_reviews))

        if cleanup_results["admin_remarks"]:
            dicts_to_save.append(("admin_remarks", save_admin_remarks))

        if cleanup_results["orders"]:
            dicts_to_save.append(("orders", save_current_orders))

        if cleanup_results["order_status"]:
            dicts_to_save.append(("order_status", save_order_status))

        if cleanup_results["awaiting_receipt"]:
            dicts_to_save.append(("awaiting_receipt", save_awaiting_receipt))

        if cleanup_results["current_order_numbers"]:
            dicts_to_save.append(("current_order_numbers", save_current_order_numbers))

        # Save each modified dictionary
        for dict_name, save_func in dicts_to_save:
            try:
                # Get the actual dictionary reference based on name
                dict_obj = globals()[dict_name]
                # Call the appropriate save function
                save_func(dict_obj)
                logger.debug(f"Successfully saved {dict_name} during cleanup")
            except Exception as save_error:
                logger.error(f"Error saving {dict_name} during cleanup: {save_error}")

        logger.info("Successfully saved all changes to disk during cleanup")
    except Exception as e:
        logger.error(f"Error saving changes to disk during cleanup: {e}")

    # Calculate success rate
    successful_ops = sum(1 for result in cleanup_results.values() if result)
    total_ops = len(cleanup_results)

    logger.info(
        f"Cleanup completed for order #{order_number}, user ID: {user_id}. {successful_ops}/{total_ops} operations succeeded."
    )
    return successful_ops > 0  # Return True if at least one operation succeeded


def get_order_history(user_id: int) -> List[Dict[str, Any]]:
    """
    Get order history for a user from Firebase.
    UPDATED: Now uses Firebase instead of local files.
    """
    try:
        user_id_str = str(user_id)
        # Get from Firebase instead of local file
        from src.firebase_db import get_user_order_history
        history_data = get_user_order_history()
        return history_data.get(user_id_str, [])
    except Exception as e:
        logger.error(f"Error getting order history from Firebase: {e}")
        return []


def save_order_to_history(user_id: int, order_data: Dict[str, Any]) -> bool:
    """Save order to history for user"""
    try:
        # Convert user_id to string for JSON compatibility
        user_id_str = str(user_id)

        # Load current order history
        order_history = load_order_history()

        # Initialize user's order history if it doesn't exist
        if user_id_str not in order_history:
            order_history[user_id_str] = []

        # Add order to history with timestamp
        if "created_at" not in order_data:
            order_data["created_at"] = datetime.datetime.now().strftime(
                "%Y-%m-%d %H:%M:%S"
            )

        # Add username if available
        if "username" not in order_data and hasattr(order_data, "from_user"):
            order_data["username"] = order_data.from_user.username

        # Append order to history
        order_history[user_id_str].append(order_data)

        # Save updated history
        save_order_history(order_history)

        logger.info(f"Order saved to history for user {user_id}")
        return True
    except Exception as e:
        logger.error(f"Error saving order to history for user {user_id}: {e}")
        return False


def get_points_balance(user_id: int) -> int:
    """Get points balance for user - always fetches the latest data from Firebase"""
    try:
        user_id_str = str(user_id)

        if USE_FIREBASE:
            # Always fetch fresh data directly from Firebase
            # This bypasses any cached data and ensures we get the most recent values
            points_data = get_user_points(user_id_str)

            # Log the retrieved points for debugging
            if points_data and user_id_str in points_data:
                points_value = int(points_data[user_id_str])
                logger.debug(
                    f"Retrieved fresh points balance from Firebase for user {user_id}: {points_value}"
                )

                # Update the in-memory cache for consistency
                user_points[user_id_str] = points_value

                return points_value
            else:
                logger.debug(f"No points found in Firebase for user {user_id}")
                return 0
        else:
            # Legacy approach using JSON file
            # Always load the most current data directly from disk
            points_data = load_points()

            # Convert user_id to string for JSON compatibility
            if user_id_str in points_data:
                return int(points_data[user_id_str])
            else:
                # If user doesn't have points yet, return 0
                return 0
    except Exception as e:
        logger.error(f"Error getting points balance for user {user_id}: {e}")
        return 0


def update_points_balance(user_id: int, points_change: int) -> bool:
    """Update points balance for user - can be positive or negative change"""
    try:
        # Convert user_id to string for JSON compatibility
        user_id_str = str(user_id)

        if USE_FIREBASE:
            # Using Firebase
            # Get current balance from Firebase
            points_data = get_user_points(user_id_str)

            # Get current balance (defaults to 0 if not found)
            current_balance = int(points_data.get(user_id_str, 0)) if points_data else 0

            # Calculate new balance
            new_balance = current_balance + points_change

            # Ensure balance never goes below zero
            if new_balance < 0:
                new_balance = 0
                logger.warning(f"Tried to set negative balance for user {user_id}")

            # Update points in Firebase
            success = update_user_points(user_id_str, new_balance)

            # Also update in-memory dict
            user_points[user_id_str] = new_balance

            logger.info(
                f"Updated points for user {user_id}: {current_balance} -> {new_balance}"
            )
            return success
        else:
            # Legacy approach using JSON files
            # Load current points data directly from disk to ensure we have the latest
            points_data = load_points()

            # Get current balance (defaults to 0 if not found)
            current_balance = int(points_data.get(user_id_str, 0))

            # Calculate new balance
            new_balance = current_balance + points_change

            # Ensure balance never goes below zero
            if new_balance < 0:
                new_balance = 0
                logger.warning(f"Tried to set negative balance for user {user_id}")

            # Update points data and save
            points_data[user_id_str] = new_balance
            user_points[user_id_str] = new_balance  # Update in-memory dict too
            save_points(points_data)  # Ensure we save to disk immediately

            logger.info(
                f"Updated points for user {user_id}: {current_balance} -> {new_balance}"
            )
            return True
    except Exception as e:
        logger.error(f"Error updating points for user {user_id}: {e}")
        return False


def save_favorite_order(
    user_id: int, favorite_name: str, order_data: Dict[str, Any]
) -> bool:
    """Save a favorite order for a user with data synchronization"""
    try:
        user_id_str = str(user_id)

        # Initialize user's favorites if not exists
        if user_id_str not in favorite_orders:
            favorite_orders[user_id_str] = []

        # Make a copy and add the favorite name
        favorite = order_data.copy()
        favorite["favorite_name"] = favorite_name

        # Add the user's name to the favorite order data if available
        if user_id_str in user_names and user_names[user_id_str]:
            favorite["user_name"] = user_names[user_id_str]

        # Store current timestamp
        favorite["timestamp"] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # Sync the order data with current database values before saving
        try:
            from src.utils.favorite_orders_sync import sync_single_favorite_order
            synced_favorite, _ = sync_single_favorite_order(favorite)
            favorite = synced_favorite
        except ImportError:
            logger.debug("Favorite orders sync module not available")
        except Exception as sync_e:
            logger.warning(f"Error syncing favorite order before save: {sync_e}")

        # Add to favorites
        favorite_orders[user_id_str].append(favorite)

        # Save to Firebase with retry logic
        success = update_favorite_orders(user_id_str, favorite_orders[user_id_str])
        if success:
            logger.info(f"Saved favorite order '{favorite_name}' for user {user_id}")

            # Validate persistence after save
            try:
                validation_result = validate_favorite_orders_persistence()
                if not validation_result["valid"]:
                    logger.warning(f"Persistence validation failed after saving favorite order for user {user_id}")
            except Exception as val_e:
                logger.warning(f"Error validating persistence after save: {val_e}")
        else:
            logger.error(f"Failed to save favorite order '{favorite_name}' to Firebase for user {user_id}")
            # Remove from local cache if Firebase save failed
            favorite_orders[user_id_str].pop()

            # Attempt retry
            logger.info(f"Retrying save for favorite order '{favorite_name}' for user {user_id}")
            retry_success = update_favorite_orders(user_id_str, favorite_orders[user_id_str])
            if retry_success:
                logger.info(f"Retry successful for favorite order '{favorite_name}' for user {user_id}")
                # Re-add to local cache
                favorite_orders[user_id_str].append(favorite)
                success = True
            else:
                logger.error(f"Retry failed for favorite order '{favorite_name}' for user {user_id}")

        return success
    except Exception as e:
        logger.error(f"Error saving favorite order: {e}")
        return False


def get_user_favorite_orders(user_id: int, sync_data: bool = True) -> List[Dict[str, Any]]:
    """Get list of favorite orders for a user with optional data synchronization"""
    try:
        user_id_str = str(user_id)

        # Ensure user's favorite orders are loaded in local cache
        if user_id_str not in favorite_orders:
            logger.debug(f"User {user_id} not found in local cache, loading from Firebase")
            try:
                from src.firebase_db import get_favorite_orders as get_firebase_favorite_orders
                user_favorites_data = get_firebase_favorite_orders(user_id_str)
                if user_favorites_data and user_id_str in user_favorites_data:
                    favorite_orders[user_id_str] = user_favorites_data[user_id_str]
                    logger.debug(f"Loaded {len(favorite_orders[user_id_str])} favorite orders for user {user_id} from Firebase")
                else:
                    logger.debug(f"No favorite orders found in Firebase for user {user_id}")
                    favorite_orders[user_id_str] = []
            except Exception as load_e:
                logger.error(f"Error loading favorite orders from Firebase for user {user_id}: {load_e}")
                favorite_orders[user_id_str] = []

        user_favorites = favorite_orders.get(user_id_str, [])

        # Sync with current database values if requested and favorites exist
        if sync_data and user_favorites:
            try:
                from src.utils.favorite_orders_sync import sync_user_favorite_orders
                sync_success = sync_user_favorite_orders(user_id_str)
                if sync_success:
                    # Reload from local cache after sync
                    user_favorites = favorite_orders.get(user_id_str, [])
            except ImportError:
                logger.debug("Favorite orders sync module not available")
            except Exception as sync_e:
                logger.warning(f"Error syncing favorite orders for user {user_id}: {sync_e}")

        return user_favorites
    except Exception as e:
        logger.error(f"Error getting user favorite orders: {e}")
        return []


def delete_favorite_order(user_id: int, index: int) -> bool:
    """Delete a favorite order by index with improved persistence"""
    try:
        user_id_str = str(user_id)

        # Ensure user's favorite orders are loaded in local cache
        if user_id_str not in favorite_orders:
            logger.debug(f"User {user_id} not found in local cache, loading from Firebase")
            try:
                from src.firebase_db import get_favorite_orders as get_firebase_favorite_orders
                user_favorites_data = get_firebase_favorite_orders(user_id_str)
                if user_favorites_data and user_id_str in user_favorites_data:
                    favorite_orders[user_id_str] = user_favorites_data[user_id_str]
                    logger.debug(f"Loaded {len(favorite_orders[user_id_str])} favorite orders for user {user_id} from Firebase")
                else:
                    logger.debug(f"No favorite orders found in Firebase for user {user_id}")
                    favorite_orders[user_id_str] = []
            except Exception as load_e:
                logger.error(f"Error loading favorite orders from Firebase for user {user_id}: {load_e}")
                return False

        # Check if user has favorite orders and index is valid
        if user_id_str in favorite_orders and 0 <= index < len(favorite_orders[user_id_str]):
            # Store the deleted order for potential rollback
            deleted_order = favorite_orders[user_id_str][index]

            # Remove the order at the specified index
            favorite_orders[user_id_str].pop(index)

            # Save to Firebase
            success = update_favorite_orders(user_id_str, favorite_orders[user_id_str])

            if success:
                logger.info(f"Successfully deleted favorite order at index {index} for user {user_id}")

                # Validate persistence after deletion
                try:
                    validation_result = validate_favorite_orders_persistence()
                    if not validation_result["valid"]:
                        logger.warning(f"Persistence validation failed after deleting favorite order for user {user_id}")
                except Exception as val_e:
                    logger.warning(f"Error validating persistence after deletion: {val_e}")

                return True
            else:
                logger.error(f"Failed to save favorite orders to Firebase after deletion for user {user_id}")
                # Rollback - re-add the deleted order
                favorite_orders[user_id_str].insert(index, deleted_order)
                return False
        else:
            logger.warning(f"Invalid deletion request for user {user_id}: index {index}, available orders: {len(favorite_orders.get(user_id_str, []))}")
            return False

    except Exception as e:
        logger.error(f"Error deleting favorite order: {e}")
        return False


def get_area_id_by_name(area_name):
    """Get area ID by name"""
    global areas_data

    # Check in dynamic areas data
    for area in areas_data.get("areas", []):
        if area["name"] == area_name:
            return area["id"]

    # If not found, generate a temporary ID
    # This is needed for compatibility with static config
    from src.config import restaurants as config_restaurants

    area_names = list(config_restaurants.keys())

    if area_name in area_names:
        # Use the position in the list + 1000 as a temporary ID
        # (to avoid conflicts with real IDs)
        return 1000 + area_names.index(area_name)

    return None


def set_current_order_area(user_id, area_name, area_id):
    """Store area information in user's current order"""
    user_id_str = str(user_id)

    # Initialize current order if needed
    if user_id_str not in orders:
        orders[user_id_str] = {}

    # Store area information
    orders[user_id_str]["area"] = area_name
    orders[user_id_str]["area_id"] = area_id

    # Set order status
    if user_id not in order_status:
        order_status[user_id] = "SELECTING_RESTAURANT"

    logger.info(f"Set order area for user {user_id} to {area_name} (ID: {area_id})")

    # Save to Firebase
    update_current_order(user_id_str, orders[user_id_str])
    update_order_status(user_id, order_status[user_id])

    return True
